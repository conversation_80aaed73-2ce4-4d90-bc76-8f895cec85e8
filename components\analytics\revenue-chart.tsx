'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DollarSign, TrendingUp } from 'lucide-react'

interface RevenueData {
  date: string
  revenue: number
  bookings: number
}

interface RevenueChartProps {
  providerId: string
}

export function RevenueChart({ providerId }: RevenueChartProps) {
  const [data, setData] = useState<RevenueData[]>([])
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30d')
  const [groupBy, setGroupBy] = useState<'day' | 'week' | 'month'>('day')

  useEffect(() => {
    fetchRevenueData()
  }, [providerId, period, groupBy])

  const fetchRevenueData = async () => {
    try {
      setLoading(true)
      
      const endDate = new Date()
      const startDate = new Date()
      
      switch (period) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1)
          break
      }

      const response = await fetch(
        `/api/analytics/provider?providerId=${providerId}&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&type=revenue&groupBy=${groupBy}`
      )

      if (!response.ok) {
        throw new Error('Failed to fetch revenue data')
      }

      const result = await response.json()
      setData(result.data)
    } catch (error) {
      console.error('Error fetching revenue data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    switch (groupBy) {
      case 'day':
        return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' })
      case 'week':
        return `S${Math.ceil(date.getDate() / 7)}`
      case 'month':
        return date.toLocaleDateString('fr-FR', { month: 'short' })
      default:
        return dateString
    }
  }

  const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0)
  const totalBookings = data.reduce((sum, item) => sum + item.bookings, 0)
  const averageRevenue = data.length > 0 ? totalRevenue / data.length : 0

  // Calculate max value for chart scaling
  const maxRevenue = Math.max(...data.map(item => item.revenue), 0)

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Évolution du chiffre d'affaires
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 animate-pulse">
            <div className="flex justify-between">
              <div className="h-4 bg-gray-200 rounded w-24"></div>
              <div className="h-4 bg-gray-200 rounded w-24"></div>
            </div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Évolution du chiffre d'affaires
          </CardTitle>
          <div className="flex space-x-2">
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7 jours</SelectItem>
                <SelectItem value="30d">30 jours</SelectItem>
                <SelectItem value="90d">90 jours</SelectItem>
                <SelectItem value="1y">1 an</SelectItem>
              </SelectContent>
            </Select>
            <Select value={groupBy} onValueChange={(value) => setGroupBy(value as any)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Par jour</SelectItem>
                <SelectItem value="week">Par semaine</SelectItem>
                <SelectItem value="month">Par mois</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <p className="text-sm text-gray-600">Total</p>
            <p className="text-2xl font-bold text-green-600">{formatCurrency(totalRevenue)}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Moyenne</p>
            <p className="text-2xl font-bold text-blue-600">{formatCurrency(averageRevenue)}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Réservations</p>
            <p className="text-2xl font-bold text-purple-600">{totalBookings}</p>
          </div>
        </div>

        {/* Simple Bar Chart */}
        {data.length > 0 ? (
          <div className="space-y-4">
            <div className="flex items-end justify-between h-64 border-b border-gray-200">
              {data.map((item, index) => {
                const height = maxRevenue > 0 ? (item.revenue / maxRevenue) * 100 : 0
                return (
                  <div key={index} className="flex flex-col items-center flex-1 mx-1">
                    <div className="relative group">
                      <div
                        className="bg-gradient-to-t from-red-600 to-red-400 rounded-t-sm transition-all duration-300 hover:from-red-700 hover:to-red-500 min-h-[4px]"
                        style={{ height: `${Math.max(height, 2)}%` }}
                      ></div>
                      
                      {/* Tooltip */}
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                        <div className="text-center">
                          <div className="font-semibold">{formatCurrency(item.revenue)}</div>
                          <div>{item.bookings} réservation{item.bookings !== 1 ? 's' : ''}</div>
                          <div className="text-gray-300">{formatDate(item.date)}</div>
                        </div>
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                      </div>
                    </div>
                    
                    <div className="text-xs text-gray-600 mt-2 text-center">
                      {formatDate(item.date)}
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Legend */}
            <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-gradient-to-t from-red-600 to-red-400 rounded mr-2"></div>
                <span>Chiffre d'affaires</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <TrendingUp className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">Aucune donnée de revenus pour cette période</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
