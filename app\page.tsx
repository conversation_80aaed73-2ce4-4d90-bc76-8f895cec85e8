'use client'

import { useEffect, useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ServiceCard } from '@/components/services/service-card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search, Star, Users, Award, Heart } from 'lucide-react'
import Link from 'next/link'

interface Service {
  id: string
  title: string
  description: string
  price?: number
  priceType: string
  city: string
  images: Array<{
    id: string
    url: string
    alt?: string
    isMain: boolean
  }>
  provider: {
    id: string
    firstName: string
    lastName: string
    avatar?: string
    city: string
  }
  category: {
    id: string
    name: string
    icon: string
  }
  averageRating: number
  _count: {
    reviews: number
    favorites: number
    bookings: number
  }
}

interface Category {
  id: string
  name: string
  nameEn: string
  icon: string
  _count: {
    services: number
  }
}

export default function HomePage() {
  const [featuredServices, setFeaturedServices] = useState<Service[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debugInfo, setDebugInfo] = useState('Loading...')

  useEffect(() => {
    setDebugInfo('Fetching data...')

    // Fetch featured services
    fetch('/api/services?limit=6')
      .then(res => res.json())
      .then(data => {
        console.log('Services fetched:', data)
        setFeaturedServices(data || [])
        setDebugInfo(`Services: ${data?.length || 0}`)
      })
      .catch(error => {
        console.error('Services error:', error)
        setDebugInfo('Services error: ' + error.message)
      })

    // Fetch categories
    fetch('/api/categories')
      .then(res => res.json())
      .then(data => {
        console.log('Categories fetched:', data)
        setCategories(data || [])
        setDebugInfo(prev => prev + `, Categories: ${data?.length || 0}`)
      })
      .catch(error => {
        console.error('Categories error:', error)
        setDebugInfo(prev => prev + ', Categories error: ' + error.message)
      })
  }, [])

  const handleSearch = () => {
    if (searchQuery.trim()) {
      window.location.href = `/services?search=${encodeURIComponent(searchQuery)}`
    }
  }

  return (
    <MainLayout showSidebar={false}>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-red-600 via-red-700 to-red-800 text-white rounded-2xl overflow-hidden mb-12">
        {/* Moroccan Pattern Background */}
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>
        
        <div className="relative px-8 py-16 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Célébrez vos moments précieux
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-red-100 max-w-3xl mx-auto">
            Trouvez les meilleurs prestataires pour vos mariages, fiançailles, anniversaires et célébrations traditionnelles marocaines
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="flex bg-white rounded-full p-2 shadow-lg">
              <Input
                type="text"
                placeholder="Rechercher des services (photographe, traiteur, décoration...)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1 border-0 bg-transparent text-gray-900 placeholder-gray-500 focus:ring-0"
              />
              <Button 
                onClick={handleSearch}
                variant="moroccan" 
                className="rounded-full px-6"
              >
                <Search className="h-5 w-5 mr-2" />
                Rechercher
              </Button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">500+</div>
              <div className="text-red-100">Prestataires vérifiés</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">1000+</div>
              <div className="text-red-100">Événements réussis</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">4.8/5</div>
              <div className="text-red-100">Note moyenne</div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="mb-12">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Explorez nos catégories
          </h2>
          <p className="text-gray-600 text-lg">
            Découvrez tous les services pour vos célébrations
          </p>
          <div className="text-sm text-gray-500 mt-2">Debug: {debugInfo}</div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {categories.slice(0, 10).map((category) => (
            <Link
              key={category.id}
              href={`/services?category=${category.id}`}
              className="group"
            >
              <div className="bg-white rounded-xl p-6 text-center shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:border-red-200">
                <div className="text-4xl mb-3 group-hover:scale-110 transition-transform duration-300">
                  {category.icon}
                </div>
                <h3 className="font-semibold text-gray-900 mb-1 text-sm">
                  {category.name}
                </h3>
                <p className="text-xs text-gray-500">
                  {category._count.services} services
                </p>
              </div>
            </Link>
          ))}
        </div>
      </section>

      {/* Featured Services */}
      <section className="mb-12">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Services populaires
            </h2>
            <p className="text-gray-600">
              Les prestataires les mieux notés de la plateforme
            </p>
          </div>
          <Link href="/services">
            <Button variant="outline">
              Voir tous les services
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredServices.map((service) => (
            <ServiceCard
              key={service.id}
              service={service}
            />
          ))}
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Pourquoi choisir Celebration Platform ?
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-red-600 to-red-700 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Prestataires vérifiés
            </h3>
            <p className="text-gray-600">
              Tous nos prestataires sont soigneusement sélectionnés et vérifiés pour garantir la qualité de vos événements.
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Star className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Avis authentiques
            </h3>
            <p className="text-gray-600">
              Consultez les avis et évaluations d'autres clients pour faire le meilleur choix pour votre événement.
            </p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Award className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Réservation simple
            </h3>
            <p className="text-gray-600">
              Réservez vos prestataires en quelques clics et gérez toutes vos réservations depuis votre tableau de bord.
            </p>
          </div>
        </div>
      </section>
    </MainLayout>
  )
}

