'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { ServiceCard } from '@/components/services/service-card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search, Filter } from 'lucide-react'

interface Service {
  id: string
  title: string
  description: string
  price?: number
  priceType: string
  city: string
  images: { url: string; isMain: boolean }[]
  provider: {
    firstName: string
    lastName: string
    avatar?: string
  }
  category: {
    name: string
  }
  subcategory?: {
    name: string
  }
  _count: {
    reviews: number
    favorites: number
    bookings: number
  }
  averageRating?: number
}

interface Category {
  id: string
  name: string
  nameEn: string
  icon: string
  description: string
  isActive: boolean
  order: number
  _count: {
    services: number
  }
}

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  
  const searchParams = useSearchParams()
  const categoryFilter = searchParams.get('category')
  const searchFilter = searchParams.get('search')

  useEffect(() => {
    // Fetch categories
    fetch('/api/categories')
      .then(res => res.json())
      .then(setCategories)
      .catch(console.error)

    // Build query parameters
    const params = new URLSearchParams()
    if (categoryFilter) params.append('category', categoryFilter)
    if (searchFilter) params.append('search', searchFilter)

    // Fetch services
    fetch(`/api/services?${params.toString()}`)
      .then(res => res.json())
      .then(data => {
        setServices(data || [])
        setLoading(false)
      })
      .catch(error => {
        console.error('Error fetching services:', error)
        setLoading(false)
      })
  }, [categoryFilter, searchFilter])

  const selectedCategory = categories.find(cat => cat.id === categoryFilter)

  const handleSearch = () => {
    if (searchQuery.trim()) {
      const params = new URLSearchParams()
      params.append('search', searchQuery)
      if (categoryFilter) params.append('category', categoryFilter)
      window.location.href = `/services?${params.toString()}`
    }
  }

  return (
    <MainLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {selectedCategory ? `Services - ${selectedCategory.name}` : 'Tous les services'}
          </h1>
          <p className="text-gray-600">
            {selectedCategory 
              ? selectedCategory.description 
              : 'Découvrez tous nos prestataires pour vos célébrations'
            }
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Rechercher des services..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-10"
                />
              </div>
            </div>
            <Button onClick={handleSearch} className="bg-gradient-to-r from-red-600 to-red-700">
              <Search className="h-4 w-4 mr-2" />
              Rechercher
            </Button>
          </div>
        </div>

        {/* Results */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des services...</p>
          </div>
        ) : services.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Aucun service trouvé
            </h3>
            <p className="text-gray-600 mb-6">
              {selectedCategory 
                ? `Aucun service disponible dans la catégorie "${selectedCategory.name}"`
                : 'Aucun service ne correspond à votre recherche'
              }
            </p>
            <Button 
              onClick={() => window.location.href = '/services'}
              variant="outline"
            >
              Voir tous les services
            </Button>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
