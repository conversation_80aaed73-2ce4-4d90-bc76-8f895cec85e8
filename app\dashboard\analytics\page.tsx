'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { AnalyticsOverview } from '@/components/analytics/analytics-overview'
import { RevenueChart } from '@/components/analytics/revenue-chart'
import { ServicePerformance } from '@/components/analytics/service-performance'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  BarChart3, 
  DollarSign, 
  TrendingUp, 
  Users, 
  Download,
  RefreshCw
} from 'lucide-react'

// Mock user data - in a real app, this would come from authentication
const mockProviderId = "cmdyuyaa0000sv1m09y3icnc8" // Using one of the seeded provider IDs

export default function AnalyticsDashboard() {
  const [refreshing, setRefreshing] = useState(false)

  const handleRefresh = async () => {
    setRefreshing(true)
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
    // In a real app, you would trigger a refresh of all analytics data
    window.location.reload()
  }

  const handleExport = () => {
    // In a real app, this would generate and download a report
    alert('Fonctionnalité d\'export en cours de développement')
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tableau de bord analytique</h1>
            <p className="text-gray-600 mt-1">
              Suivez les performances de vos services et analysez vos revenus
            </p>
          </div>
          
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Actualiser
            </Button>
            <Button
              onClick={handleExport}
              className="bg-red-600 hover:bg-red-700 text-white flex items-center"
            >
              <Download className="h-4 w-4 mr-2" />
              Exporter
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Revenus ce mois</p>
                  <p className="text-2xl font-bold text-green-600">12 450 MAD</p>
                  <p className="text-sm text-green-600 flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +15.3% vs mois dernier
                  </p>
                </div>
                <div className="p-3 rounded-full bg-green-100">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Réservations</p>
                  <p className="text-2xl font-bold text-blue-600">28</p>
                  <p className="text-sm text-blue-600 flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +8.2% vs mois dernier
                  </p>
                </div>
                <div className="p-3 rounded-full bg-blue-100">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Nouveaux clients</p>
                  <p className="text-2xl font-bold text-purple-600">15</p>
                  <p className="text-sm text-purple-600 flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +22.1% vs mois dernier
                  </p>
                </div>
                <div className="p-3 rounded-full bg-purple-100">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Taux de conversion</p>
                  <p className="text-2xl font-bold text-orange-600">18.5%</p>
                  <p className="text-sm text-orange-600 flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +3.2% vs mois dernier
                  </p>
                </div>
                <div className="p-3 rounded-full bg-orange-100">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Analytics Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="revenue">Revenus</TabsTrigger>
            <TabsTrigger value="services">Services</TabsTrigger>
            <TabsTrigger value="customers">Clients</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <AnalyticsOverview providerId={mockProviderId} />
          </TabsContent>

          <TabsContent value="revenue" className="space-y-6">
            <RevenueChart providerId={mockProviderId} />
            
            {/* Additional revenue insights */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Répartition des revenus</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Services de mariage</span>
                      <div className="flex items-center">
                        <div className="w-24 h-2 bg-gray-200 rounded-full mr-3">
                          <div className="w-3/4 h-2 bg-red-600 rounded-full"></div>
                        </div>
                        <span className="font-semibold">75%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Anniversaires</span>
                      <div className="flex items-center">
                        <div className="w-24 h-2 bg-gray-200 rounded-full mr-3">
                          <div className="w-1/2 h-2 bg-blue-600 rounded-full"></div>
                        </div>
                        <span className="font-semibold">15%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Autres événements</span>
                      <div className="flex items-center">
                        <div className="w-24 h-2 bg-gray-200 rounded-full mr-3">
                          <div className="w-1/4 h-2 bg-green-600 rounded-full"></div>
                        </div>
                        <span className="font-semibold">10%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Objectifs mensuels</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-gray-600">Revenus</span>
                        <span className="font-semibold">12 450 / 15 000 MAD</span>
                      </div>
                      <div className="w-full h-3 bg-gray-200 rounded-full">
                        <div className="w-4/5 h-3 bg-green-600 rounded-full"></div>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">83% de l'objectif atteint</p>
                    </div>
                    
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-gray-600">Réservations</span>
                        <span className="font-semibold">28 / 30</span>
                      </div>
                      <div className="w-full h-3 bg-gray-200 rounded-full">
                        <div className="w-11/12 h-3 bg-blue-600 rounded-full"></div>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">93% de l'objectif atteint</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="services" className="space-y-6">
            <ServicePerformance providerId={mockProviderId} />
          </TabsContent>

          <TabsContent value="customers" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Top clients</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { name: 'Sarah Alami', spent: 4500, bookings: 3 },
                      { name: 'Mohamed Benali', spent: 3200, bookings: 2 },
                      { name: 'Fatima Zahra', spent: 2800, bookings: 2 },
                      { name: 'Ahmed Tazi', spent: 2100, bookings: 1 },
                      { name: 'Laila Mansouri', spent: 1900, bookings: 1 }
                    ].map((customer, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{customer.name}</p>
                          <p className="text-sm text-gray-600">{customer.bookings} réservation{customer.bookings > 1 ? 's' : ''}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-green-600">{customer.spent.toLocaleString()} MAD</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Satisfaction client</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="text-4xl font-bold text-yellow-600 mb-2">4.8</div>
                      <div className="flex justify-center mb-2">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <span key={star} className="text-yellow-500 text-xl">★</span>
                        ))}
                      </div>
                      <p className="text-gray-600">Note moyenne sur 45 avis</p>
                    </div>
                    
                    <div className="space-y-2">
                      {[
                        { stars: 5, count: 32, percentage: 71 },
                        { stars: 4, count: 10, percentage: 22 },
                        { stars: 3, count: 2, percentage: 4 },
                        { stars: 2, count: 1, percentage: 2 },
                        { stars: 1, count: 0, percentage: 0 }
                      ].map((rating) => (
                        <div key={rating.stars} className="flex items-center space-x-2">
                          <span className="text-sm w-8">{rating.stars}★</span>
                          <div className="flex-1 h-2 bg-gray-200 rounded-full">
                            <div 
                              className="h-2 bg-yellow-500 rounded-full"
                              style={{ width: `${rating.percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600 w-8">{rating.count}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
