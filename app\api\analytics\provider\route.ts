import { NextRequest, NextResponse } from 'next/server'
import AnalyticsService from '@/lib/analytics/analytics-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const providerId = searchParams.get('providerId')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const type = searchParams.get('type') || 'overview'

    if (!providerId) {
      return NextResponse.json(
        { error: 'Provider ID is required' },
        { status: 400 }
      )
    }

    // Default to last 30 days if no dates provided
    const end = endDate ? new Date(endDate) : new Date()
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

    let data: any

    switch (type) {
      case 'overview':
        data = await AnalyticsService.getProviderAnalytics(providerId, start, end)
        break
      
      case 'services':
        data = await AnalyticsService.getServiceAnalytics(providerId, start, end)
        break
      
      case 'revenue':
        const groupBy = searchParams.get('groupBy') as 'day' | 'week' | 'month' || 'day'
        data = await AnalyticsService.getRevenueData(providerId, start, end, groupBy)
        break
      
      case 'customers':
        data = await AnalyticsService.getCustomerAnalytics(providerId, start, end)
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid analytics type' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      data,
      period: {
        startDate: start.toISOString(),
        endDate: end.toISOString()
      }
    })

  } catch (error) {
    console.error('Error fetching provider analytics:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch analytics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
