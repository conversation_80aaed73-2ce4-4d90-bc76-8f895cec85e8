# 🚀 Feature Implementation Guide - Celebration Platform

## Quick Start Implementation Order

### 🔥 IMMEDIATE PRIORITY (Week 1-2)

#### 1. Fix Client-Side Hydration Issue
```typescript
// app/page.tsx - Fix the current data loading issue
'use client'
import { useEffect, useState } from 'react'

export default function HomePage() {
  const [mounted, setMounted] = useState(false)
  const [categories, setCategories] = useState([])
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted) return

    const fetchData = async () => {
      try {
        const [categoriesRes, servicesRes] = await Promise.all([
          fetch('/api/categories'),
          fetch('/api/services?limit=6')
        ])
        
        const categoriesData = await categoriesRes.json()
        const servicesData = await servicesRes.json()
        
        setCategories(categoriesData)
        setServices(servicesData)
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [mounted])

  if (!mounted || loading) {
    return <LoadingSkeleton />
  }

  return (
    <div>
      {/* Your existing JSX with categories and services */}
    </div>
  )
}
```

#### 2. Add Loading States
```typescript
// components/ui/loading-skeleton.tsx
export function LoadingSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {Array.from({ length: 10 }).map((_, i) => (
          <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
        ))}
      </div>
    </div>
  )
}
```

### 🎯 HIGH PRIORITY (Week 3-8)

#### 3. Enhanced Booking System
```typescript
// components/booking/booking-wizard.tsx
export function BookingWizard({ serviceId }: { serviceId: string }) {
  const [step, setStep] = useState(1)
  const [bookingData, setBookingData] = useState({
    eventDate: '',
    eventType: '',
    guestCount: 0,
    location: '',
    packages: [],
    addOns: [],
    totalAmount: 0
  })

  const steps = [
    { id: 1, title: 'Event Details', component: EventDetailsStep },
    { id: 2, title: 'Packages & Add-ons', component: PackagesStep },
    { id: 3, title: 'Payment', component: PaymentStep },
    { id: 4, title: 'Confirmation', component: ConfirmationStep }
  ]

  return (
    <div className="max-w-4xl mx-auto">
      <StepIndicator currentStep={step} steps={steps} />
      <div className="mt-8">
        {steps.find(s => s.id === step)?.component({ 
          data: bookingData, 
          onUpdate: setBookingData,
          onNext: () => setStep(step + 1),
          onPrev: () => setStep(step - 1)
        })}
      </div>
    </div>
  )
}
```

#### 4. Payment Integration
```typescript
// lib/payment/stripe-client.ts
import { loadStripe } from '@stripe/stripe-js'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

export async function createPaymentIntent(amount: number, currency = 'mad') {
  const response = await fetch('/api/payments/create-intent', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ amount, currency })
  })
  
  return response.json()
}

// components/payment/payment-form.tsx
export function PaymentForm({ amount, onSuccess }: PaymentFormProps) {
  const stripe = useStripe()
  const elements = useElements()

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault()
    
    if (!stripe || !elements) return

    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/booking/success`
      }
    })

    if (error) {
      console.error('Payment failed:', error)
    } else {
      onSuccess(paymentIntent)
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      <PaymentElement />
      <button type="submit" disabled={!stripe}>
        Pay {amount} MAD
      </button>
    </form>
  )
}
```

#### 5. Real-time Messaging
```typescript
// lib/socket/socket-client.ts
import { io, Socket } from 'socket.io-client'

class SocketClient {
  private socket: Socket | null = null

  connect(userId: string) {
    this.socket = io('/api/socket', {
      auth: { userId }
    })

    this.socket.on('connect', () => {
      console.log('Connected to server')
    })

    this.socket.on('message:new', (message) => {
      // Handle new message
    })

    this.socket.on('booking:updated', (booking) => {
      // Handle booking update
    })
  }

  sendMessage(conversationId: string, content: string) {
    this.socket?.emit('message:send', { conversationId, content })
  }

  disconnect() {
    this.socket?.disconnect()
  }
}

export const socketClient = new SocketClient()
```

#### 6. Advanced Review System
```typescript
// components/reviews/review-form.tsx
export function ReviewForm({ serviceId, onSubmit }: ReviewFormProps) {
  const [ratings, setRatings] = useState({
    overall: 0,
    quality: 0,
    value: 0,
    service: 0,
    communication: 0
  })
  const [comment, setComment] = useState('')
  const [images, setImages] = useState<File[]>([])

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    
    const formData = new FormData()
    formData.append('serviceId', serviceId)
    formData.append('ratings', JSON.stringify(ratings))
    formData.append('comment', comment)
    
    images.forEach((image, index) => {
      formData.append(`image_${index}`, image)
    })

    const response = await fetch('/api/reviews', {
      method: 'POST',
      body: formData
    })

    if (response.ok) {
      onSubmit()
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.entries(ratings).map(([key, value]) => (
          <div key={key}>
            <label className="block text-sm font-medium mb-2">
              {key.charAt(0).toUpperCase() + key.slice(1)} Rating
            </label>
            <StarRating
              value={value}
              onChange={(rating) => setRatings(prev => ({ ...prev, [key]: rating }))}
            />
          </div>
        ))}
      </div>
      
      <textarea
        value={comment}
        onChange={(e) => setComment(e.target.value)}
        placeholder="Share your experience..."
        className="w-full p-3 border rounded-lg"
        rows={4}
      />
      
      <ImageUpload
        images={images}
        onChange={setImages}
        maxImages={5}
      />
      
      <button type="submit" className="w-full bg-red-600 text-white py-3 rounded-lg">
        Submit Review
      </button>
    </form>
  )
}
```

### 🎨 MEDIUM PRIORITY (Week 9-16)

#### 7. AI Recommendation Engine
```typescript
// lib/ai/recommendation-engine.ts
export class RecommendationEngine {
  async generateRecommendations(userId: string): Promise<Recommendation[]> {
    const userProfile = await this.getUserProfile(userId)
    const userBehavior = await this.getUserBehavior(userId)
    
    // Collaborative filtering
    const similarUsers = await this.findSimilarUsers(userId)
    const collaborativeRecs = await this.getCollaborativeRecommendations(similarUsers)
    
    // Content-based filtering
    const contentRecs = await this.getContentBasedRecommendations(userProfile)
    
    // Trending services
    const trendingRecs = await this.getTrendingRecommendations()
    
    // Combine and score recommendations
    return this.combineRecommendations([
      { recommendations: collaborativeRecs, weight: 0.4 },
      { recommendations: contentRecs, weight: 0.4 },
      { recommendations: trendingRecs, weight: 0.2 }
    ])
  }

  private async getUserProfile(userId: string) {
    return prisma.userPreference.findUnique({
      where: { userId },
      include: { user: true }
    })
  }

  private async findSimilarUsers(userId: string) {
    // Implement user similarity algorithm
    // Based on booking history, preferences, ratings
  }
}
```

#### 8. Advanced Search with Elasticsearch
```typescript
// lib/search/elasticsearch-client.ts
import { Client } from '@elastic/elasticsearch'

export class SearchService {
  private client: Client

  constructor() {
    this.client = new Client({
      node: process.env.ELASTICSEARCH_URL
    })
  }

  async searchServices(query: string, filters: SearchFilters) {
    const searchBody = {
      query: {
        bool: {
          must: [
            {
              multi_match: {
                query,
                fields: ['title^2', 'description', 'tags'],
                fuzziness: 'AUTO'
              }
            }
          ],
          filter: this.buildFilters(filters)
        }
      },
      aggs: {
        categories: { terms: { field: 'categoryId' } },
        price_ranges: {
          range: {
            field: 'price',
            ranges: [
              { to: 1000 },
              { from: 1000, to: 5000 },
              { from: 5000, to: 10000 },
              { from: 10000 }
            ]
          }
        },
        cities: { terms: { field: 'city' } }
      },
      sort: this.buildSort(filters.sortBy)
    }

    const response = await this.client.search({
      index: 'services',
      body: searchBody
    })

    return {
      services: response.body.hits.hits.map(hit => hit._source),
      aggregations: response.body.aggregations,
      total: response.body.hits.total.value
    }
  }

  private buildFilters(filters: SearchFilters) {
    const filterClauses = []

    if (filters.categoryId) {
      filterClauses.push({ term: { categoryId: filters.categoryId } })
    }

    if (filters.city) {
      filterClauses.push({ term: { city: filters.city } })
    }

    if (filters.priceRange) {
      filterClauses.push({
        range: {
          price: {
            gte: filters.priceRange.min,
            lte: filters.priceRange.max
          }
        }
      })
    }

    if (filters.rating) {
      filterClauses.push({
        range: {
          averageRating: { gte: filters.rating }
        }
      })
    }

    return filterClauses
  }
}
```

#### 9. Provider Analytics Dashboard
```typescript
// components/dashboard/analytics-dashboard.tsx
export function AnalyticsDashboard({ providerId }: { providerId: string }) {
  const { data: insights } = useSWR(
    `/api/analytics/provider/${providerId}`,
    fetcher
  )

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <MetricCard
        title="Total Views"
        value={insights?.totalViews}
        change={insights?.viewsChange}
        icon={EyeIcon}
      />
      
      <MetricCard
        title="Bookings"
        value={insights?.totalBookings}
        change={insights?.bookingsChange}
        icon={CalendarIcon}
      />
      
      <MetricCard
        title="Revenue"
        value={`${insights?.totalRevenue} MAD`}
        change={insights?.revenueChange}
        icon={CurrencyDollarIcon}
      />
      
      <MetricCard
        title="Rating"
        value={insights?.averageRating}
        change={insights?.ratingChange}
        icon={StarIcon}
      />

      <div className="col-span-full">
        <RevenueChart data={insights?.revenueData} />
      </div>

      <div className="col-span-full lg:col-span-2">
        <BookingTrendsChart data={insights?.bookingTrends} />
      </div>

      <div className="col-span-full lg:col-span-2">
        <CustomerDemographics data={insights?.demographics} />
      </div>
    </div>
  )
}
```

### 🌟 ADVANCED FEATURES (Week 17+)

#### 10. Social Features
```typescript
// components/social/social-feed.tsx
export function SocialFeed() {
  const [posts, setPosts] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPosts()
  }, [])

  const fetchPosts = async () => {
    const response = await fetch('/api/social/feed')
    const data = await response.json()
    setPosts(data)
    setLoading(false)
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <CreatePostForm onPostCreated={fetchPosts} />
      
      {loading ? (
        <PostSkeleton />
      ) : (
        posts.map(post => (
          <PostCard key={post.id} post={post} />
        ))
      )}
    </div>
  )
}
```

This implementation guide provides practical, ready-to-use code examples for implementing the most important features. Each section includes working TypeScript/React code that can be directly integrated into your platform.
