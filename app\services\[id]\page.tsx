'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { BookingWizard, BookingData } from '@/components/booking/booking-wizard'
import { RecommendationsSection } from '@/components/recommendations/recommendations-section'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { 
  MapPin, 
  User, 
  Star, 
  Calendar, 
  Heart, 
  Share2, 
  Phone, 
  Mail,
  Clock,
  Users,
  Award
} from 'lucide-react'
import Link from 'next/link'

interface Service {
  id: string
  title: string
  description: string
  price?: number
  priceType: string
  city: string
  images: Array<{
    id: string
    url: string
    alt?: string
    isMain: boolean
  }>
  provider: {
    id: string
    firstName: string
    lastName: string
    avatar?: string
    city: string
    phone?: string
    email?: string
  }
  category: {
    id: string
    name: string
    icon: string
  }
  subcategory?: {
    id: string
    name: string
  }
  averageRating: number
  _count: {
    reviews: number
    favorites: number
    bookings: number
  }
  createdAt: string
  updatedAt: string
}

export default function ServiceDetailPage() {
  const params = useParams()
  const serviceId = params.id as string
  
  const [service, setService] = useState<Service | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showBookingWizard, setShowBookingWizard] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)

  useEffect(() => {
    const fetchService = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/services/${serviceId}`)
        
        if (!response.ok) {
          throw new Error('Service non trouvé')
        }
        
        const serviceData = await response.json()
        setService(serviceData)
      } catch (error) {
        console.error('Error fetching service:', error)
        setError(error instanceof Error ? error.message : 'Erreur lors du chargement')
      } finally {
        setLoading(false)
      }
    }

    if (serviceId) {
      fetchService()
    }
  }, [serviceId])

  const handleBookingComplete = async (bookingData: BookingData) => {
    try {
      // Here you would send the booking data to your API
      console.log('Booking completed:', bookingData)
      
      // For now, just close the wizard and show a success message
      setShowBookingWizard(false)
      alert('Réservation confirmée ! Vous recevrez un email de confirmation.')
    } catch (error) {
      console.error('Error completing booking:', error)
      alert('Erreur lors de la réservation. Veuillez réessayer.')
    }
  }

  const formatPrice = (price: number, priceType: string) => {
    const formatted = new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(price)

    switch (priceType) {
      case 'PER_HOUR':
        return `${formatted}/heure`
      case 'PER_DAY':
        return `${formatted}/jour`
      case 'NEGOTIABLE':
        return `À partir de ${formatted}`
      default:
        return formatted
    }
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="animate-pulse space-y-6">
          <div className="h-64 bg-gray-200 rounded-lg"></div>
          <div className="space-y-4">
            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </div>
      </MainLayout>
    )
  }

  if (error || !service) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <div className="text-red-600 text-lg mb-4">⚠️ Service non trouvé</div>
          <p className="text-gray-600 mb-4">{error || 'Ce service n\'existe pas ou a été supprimé.'}</p>
          <Link href="/services">
            <Button>Retour aux services</Button>
          </Link>
        </div>
      </MainLayout>
    )
  }

  const mainImage = service.images.find(img => img.isMain) || service.images[0]

  return (
    <MainLayout>
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
              <Link href="/services" className="hover:text-red-600">Services</Link>
              <span>›</span>
              <Link href={`/services?category=${service.category.id}`} className="hover:text-red-600">
                {service.category.name}
              </Link>
              <span>›</span>
              <span>{service.title}</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900">{service.title}</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFavorited(!isFavorited)}
              className={isFavorited ? 'text-red-600 border-red-600' : ''}
            >
              <Heart className={`h-4 w-4 mr-2 ${isFavorited ? 'fill-current' : ''}`} />
              {isFavorited ? 'Favoris' : 'Ajouter aux favoris'}
            </Button>
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              Partager
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Image Gallery */}
            <div className="space-y-4">
              <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden">
                {mainImage ? (
                  <img
                    src={mainImage.url}
                    alt={mainImage.alt || service.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-red-100 to-yellow-100 flex items-center justify-center">
                    <span className="text-6xl">{service.category.icon}</span>
                  </div>
                )}
              </div>
              
              {service.images.length > 1 && (
                <div className="grid grid-cols-4 gap-2">
                  {service.images.slice(1, 5).map((image) => (
                    <div key={image.id} className="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                      <img
                        src={image.url}
                        alt={image.alt || service.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Description */}
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Description</h2>
                <p className="text-gray-700 leading-relaxed">{service.description}</p>
              </CardContent>
            </Card>

            {/* Provider Info */}
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">À propos du prestataire</h2>
                <div className="flex items-start space-x-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-full overflow-hidden">
                    {service.provider.avatar ? (
                      <img
                        src={service.provider.avatar}
                        alt={`${service.provider.firstName} ${service.provider.lastName}`}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-red-100 to-yellow-100 flex items-center justify-center">
                        <User className="h-8 w-8 text-gray-600" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">
                      {service.provider.firstName} {service.provider.lastName}
                    </h3>
                    <div className="flex items-center text-sm text-gray-600 mt-1">
                      <MapPin className="h-4 w-4 mr-1" />
                      {service.provider.city}
                    </div>
                    <div className="flex items-center space-x-4 mt-2">
                      {service.provider.phone && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Phone className="h-4 w-4 mr-1" />
                          {service.provider.phone}
                        </div>
                      )}
                      {service.provider.email && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Mail className="h-4 w-4 mr-1" />
                          {service.provider.email}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Booking Card */}
            <Card className="sticky top-6">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Price */}
                  {service.price && (
                    <div className="text-center">
                      <div className="text-3xl font-bold text-red-600">
                        {formatPrice(service.price, service.priceType)}
                      </div>
                    </div>
                  )}

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="flex items-center justify-center mb-1">
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      </div>
                      <div className="text-sm font-medium">{service.averageRating || 'N/A'}</div>
                      <div className="text-xs text-gray-500">{service._count.reviews} avis</div>
                    </div>
                    <div>
                      <div className="flex items-center justify-center mb-1">
                        <Calendar className="h-4 w-4 text-blue-500" />
                      </div>
                      <div className="text-sm font-medium">{service._count.bookings}</div>
                      <div className="text-xs text-gray-500">réservations</div>
                    </div>
                    <div>
                      <div className="flex items-center justify-center mb-1">
                        <Award className="h-4 w-4 text-green-500" />
                      </div>
                      <div className="text-sm font-medium">Vérifié</div>
                      <div className="text-xs text-gray-500">prestataire</div>
                    </div>
                  </div>

                  {/* Book Button */}
                  <Button
                    onClick={() => setShowBookingWizard(true)}
                    className="w-full bg-red-600 hover:bg-red-700 text-white"
                    size="lg"
                  >
                    Réserver maintenant
                  </Button>

                  {/* Contact Button */}
                  <Button variant="outline" className="w-full">
                    Contacter le prestataire
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Service Info */}
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Informations du service</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Catégorie</span>
                    <Badge variant="secondary">
                      {service.category.icon} {service.category.name}
                    </Badge>
                  </div>
                  {service.subcategory && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Sous-catégorie</span>
                      <span className="text-sm font-medium">{service.subcategory.name}</span>
                    </div>
                  )}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Localisation</span>
                    <span className="text-sm font-medium">{service.city}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Membre depuis</span>
                    <span className="text-sm font-medium">
                      {new Date(service.createdAt).toLocaleDateString('fr-FR')}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Similar Services Recommendations */}
      <div className="max-w-6xl mx-auto mt-12">
        <RecommendationsSection
          serviceId={service.id}
          type="similar"
          title="Services similaires"
          subtitle="Découvrez d'autres services qui pourraient vous intéresser"
          limit={6}
          showReasons={true}
        />
      </div>

      {/* Booking Wizard */}
      {showBookingWizard && (
        <BookingWizard
          serviceId={service.id}
          service={service}
          onClose={() => setShowBookingWizard(false)}
          onComplete={handleBookingComplete}
        />
      )}
    </MainLayout>
  )
}
