'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { RecommendationsSection } from '@/components/recommendations/recommendations-section'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Heart, 
  Settings,
  User,
  Brain,
  Target
} from 'lucide-react'

// Mock user ID - in a real app, this would come from authentication
const mockUserId = "cmdyuyaa0000sv1m09y3icnc8"

export default function RecommendationsPage() {
  const [activeTab, setActiveTab] = useState('personalized')

  return (
    <MainLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-3">
            <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full">
              <Brain className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">
              Recommandations IA
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Découvrez des services personnalisés grâce à notre intelligence artificielle qui apprend de vos préférences
          </p>
        </div>

        {/* AI Insights Card */}
        <Card className="bg-gradient-to-r from-purple-50 via-blue-50 to-indigo-50 border-purple-200">
          <CardHeader>
            <CardTitle className="flex items-center text-purple-900">
              <Sparkles className="h-6 w-6 mr-3" />
              Comment fonctionne notre IA ?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="font-semibold text-purple-900 mb-2">Analyse comportementale</h3>
                <p className="text-purple-700 text-sm">
                  Nous analysons vos réservations passées, vos favoris et vos préférences
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="font-semibold text-blue-900 mb-2">Intelligence artificielle</h3>
                <p className="text-blue-700 text-sm">
                  Notre algorithme trouve des correspondances parfaites basées sur vos goûts
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Target className="h-8 w-8 text-indigo-600" />
                </div>
                <h3 className="font-semibold text-indigo-900 mb-2">Recommandations précises</h3>
                <p className="text-indigo-700 text-sm">
                  Recevez des suggestions personnalisées qui correspondent vraiment à vos besoins
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recommendations Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
          <TabsList className="grid w-full grid-cols-3 max-w-md mx-auto">
            <TabsTrigger value="personalized" className="flex items-center">
              <Sparkles className="h-4 w-4 mr-2" />
              Pour vous
            </TabsTrigger>
            <TabsTrigger value="trending" className="flex items-center">
              <TrendingUp className="h-4 w-4 mr-2" />
              Tendances
            </TabsTrigger>
            <TabsTrigger value="favorites" className="flex items-center">
              <Heart className="h-4 w-4 mr-2" />
              Favoris
            </TabsTrigger>
          </TabsList>

          <TabsContent value="personalized" className="space-y-8">
            <RecommendationsSection
              userId={mockUserId}
              type="personalized"
              title="Recommandations personnalisées"
              subtitle="Services sélectionnés spécialement pour vous par notre IA"
              limit={12}
              showReasons={true}
            />
          </TabsContent>

          <TabsContent value="trending" className="space-y-8">
            <RecommendationsSection
              type="trending"
              title="Services tendance"
              subtitle="Les services les plus populaires et les mieux notés du moment"
              limit={12}
              showReasons={false}
            />
          </TabsContent>

          <TabsContent value="favorites" className="space-y-8">
            <Card>
              <CardContent className="p-12 text-center">
                <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Vos services favoris
                </h3>
                <p className="text-gray-600 mb-6">
                  Ajoutez des services à vos favoris pour les retrouver facilement ici
                </p>
                <Button variant="outline">
                  Découvrir des services
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Preference Settings */}
        <Card className="bg-gray-50">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Améliorer vos recommandations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Vos préférences actuelles</h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Catégories préférées</span>
                    <span className="text-sm font-medium">Mariages, Anniversaires</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Budget moyen</span>
                    <span className="text-sm font-medium">2 000 - 5 000 MAD</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Villes préférées</span>
                    <span className="text-sm font-medium">Casablanca, Rabat</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Actions pour améliorer l'IA</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                    <span className="text-sm">Notez plus de services</span>
                    <Button size="sm" variant="outline">Noter</Button>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                    <span className="text-sm">Ajoutez des favoris</span>
                    <Button size="sm" variant="outline">Explorer</Button>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                    <span className="text-sm">Complétez votre profil</span>
                    <Button size="sm" variant="outline">Compléter</Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">95%</div>
              <div className="text-sm text-gray-600">Précision des recommandations</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">1,247</div>
              <div className="text-sm text-gray-600">Services analysés</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">24h</div>
              <div className="text-sm text-gray-600">Mise à jour des recommandations</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">4.8/5</div>
              <div className="text-sm text-gray-600">Satisfaction utilisateur</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
