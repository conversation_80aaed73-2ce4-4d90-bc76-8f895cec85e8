import { Server as NetServer } from 'http'
import { NextApiRequest, NextApiResponse } from 'next'
import { Server as ServerIO } from 'socket.io'
import { prisma } from '@/lib/prisma'

export type NextApiResponseServerIO = NextApiResponse & {
  socket: {
    server: NetServer & {
      io: ServerIO
    }
  }
}

export interface SocketUser {
  id: string
  email: string
  firstName: string
  lastName: string
  role: string
}

export interface MessageData {
  id?: string
  content: string
  senderId: string
  receiverId: string
  bookingId?: string
  type: 'text' | 'image' | 'file' | 'system'
  metadata?: Record<string, any>
}

export class SocketService {
  private io: ServerIO
  private connectedUsers: Map<string, string> = new Map() // userId -> socketId

  constructor(io: ServerIO) {
    this.io = io
    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log('User connected:', socket.id)

      // Handle user authentication
      socket.on('authenticate', async (data: { userId: string, token?: string }) => {
        try {
          // In a real app, you would verify the token here
          const user = await prisma.user.findUnique({
            where: { id: data.userId },
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              role: true
            }
          })

          if (user) {
            socket.data.user = user
            this.connectedUsers.set(user.id, socket.id)
            
            // Join user to their personal room
            socket.join(`user:${user.id}`)
            
            // Join user to their booking rooms
            const bookings = await prisma.booking.findMany({
              where: {
                OR: [
                  { clientId: user.id },
                  { providerId: user.id }
                ]
              },
              select: { id: true }
            })

            bookings.forEach(booking => {
              socket.join(`booking:${booking.id}`)
            })

            socket.emit('authenticated', { user })
            console.log('User authenticated:', user.email)
          } else {
            socket.emit('authentication_error', { message: 'User not found' })
          }
        } catch (error) {
          console.error('Authentication error:', error)
          socket.emit('authentication_error', { message: 'Authentication failed' })
        }
      })

      // Handle sending messages
      socket.on('send_message', async (data: MessageData) => {
        try {
          if (!socket.data.user) {
            socket.emit('error', { message: 'Not authenticated' })
            return
          }

          // Validate message data
          if (!data.content || !data.receiverId) {
            socket.emit('error', { message: 'Invalid message data' })
            return
          }

          // Create message in database
          const message = await prisma.message.create({
            data: {
              content: data.content,
              senderId: socket.data.user.id,
              receiverId: data.receiverId,
              bookingId: data.bookingId,
              type: data.type || 'text',
              metadata: data.metadata || {}
            },
            include: {
              sender: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  avatar: true
                }
              },
              receiver: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  avatar: true
                }
              }
            }
          })

          // Send message to receiver if online
          const receiverSocketId = this.connectedUsers.get(data.receiverId)
          if (receiverSocketId) {
            this.io.to(receiverSocketId).emit('new_message', message)
          }

          // Send confirmation to sender
          socket.emit('message_sent', message)

          // If it's a booking-related message, send to booking room
          if (data.bookingId) {
            socket.to(`booking:${data.bookingId}`).emit('booking_message', message)
          }

          console.log('Message sent:', message.id)
        } catch (error) {
          console.error('Error sending message:', error)
          socket.emit('error', { message: 'Failed to send message' })
        }
      })

      // Handle joining conversation
      socket.on('join_conversation', async (data: { userId: string, bookingId?: string }) => {
        try {
          if (!socket.data.user) {
            socket.emit('error', { message: 'Not authenticated' })
            return
          }

          const conversationRoom = data.bookingId 
            ? `booking:${data.bookingId}`
            : `conversation:${[socket.data.user.id, data.userId].sort().join(':')}`

          socket.join(conversationRoom)

          // Load recent messages
          const messages = await prisma.message.findMany({
            where: data.bookingId ? {
              bookingId: data.bookingId
            } : {
              OR: [
                { senderId: socket.data.user.id, receiverId: data.userId },
                { senderId: data.userId, receiverId: socket.data.user.id }
              ]
            },
            include: {
              sender: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  avatar: true
                }
              }
            },
            orderBy: { createdAt: 'desc' },
            take: 50
          })

          socket.emit('conversation_history', messages.reverse())
        } catch (error) {
          console.error('Error joining conversation:', error)
          socket.emit('error', { message: 'Failed to join conversation' })
        }
      })

      // Handle typing indicators
      socket.on('typing_start', (data: { receiverId: string, bookingId?: string }) => {
        if (!socket.data.user) return

        const receiverSocketId = this.connectedUsers.get(data.receiverId)
        if (receiverSocketId) {
          this.io.to(receiverSocketId).emit('user_typing', {
            userId: socket.data.user.id,
            userName: `${socket.data.user.firstName} ${socket.data.user.lastName}`,
            bookingId: data.bookingId
          })
        }
      })

      socket.on('typing_stop', (data: { receiverId: string, bookingId?: string }) => {
        if (!socket.data.user) return

        const receiverSocketId = this.connectedUsers.get(data.receiverId)
        if (receiverSocketId) {
          this.io.to(receiverSocketId).emit('user_stopped_typing', {
            userId: socket.data.user.id,
            bookingId: data.bookingId
          })
        }
      })

      // Handle marking messages as read
      socket.on('mark_as_read', async (data: { messageIds: string[] }) => {
        try {
          if (!socket.data.user) return

          await prisma.message.updateMany({
            where: {
              id: { in: data.messageIds },
              receiverId: socket.data.user.id
            },
            data: {
              isRead: true,
              readAt: new Date()
            }
          })

          socket.emit('messages_marked_read', { messageIds: data.messageIds })
        } catch (error) {
          console.error('Error marking messages as read:', error)
        }
      })

      // Handle disconnect
      socket.on('disconnect', () => {
        if (socket.data.user) {
          this.connectedUsers.delete(socket.data.user.id)
          console.log('User disconnected:', socket.data.user.email)
        }
      })
    })
  }

  // Send system message
  async sendSystemMessage(bookingId: string, content: string, metadata?: Record<string, any>) {
    try {
      const booking = await prisma.booking.findUnique({
        where: { id: bookingId },
        include: {
          client: true,
          provider: true
        }
      })

      if (!booking) return

      const message = await prisma.message.create({
        data: {
          content,
          senderId: booking.providerId, // System messages from provider
          receiverId: booking.clientId,
          bookingId,
          type: 'system',
          metadata: metadata || {}
        },
        include: {
          sender: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true
            }
          }
        }
      })

      // Send to booking room
      this.io.to(`booking:${bookingId}`).emit('system_message', message)

      return message
    } catch (error) {
      console.error('Error sending system message:', error)
    }
  }

  // Get online users
  getOnlineUsers(): string[] {
    return Array.from(this.connectedUsers.keys())
  }

  // Check if user is online
  isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId)
  }
}

export default SocketService
