# 📚 API Documentation - Enhanced Celebration Platform

## Base URL
```
Production: https://celebration-platform.vercel.app/api
Development: http://localhost:3000/api
```

## Authentication
All protected endpoints require authentication via J<PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## API Versioning
Current version: `v1`
All endpoints are prefixed with `/api/v1/`

---

## 🔐 Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "firstName": "<PERSON>",
  "lastName": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "phone": "+************",
  "userType": "CLIENT" | "PROVIDER",
  "city": "Casablanca"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "cuid",
    "firstName": "<PERSON>",
    "lastName": "<PERSON><PERSON>",
    "email": "<EMAIL>",
    "userType": "CLIENT"
  },
  "token": "jwt_token_here"
}
```

### POST /auth/login
Authenticate user and return JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

### POST /auth/forgot-password
Send password reset email.

### POST /auth/reset-password
Reset password with token.

---

## 👤 User Management

### GET /users/profile
Get current user profile (requires authentication).

**Response:**
```json
{
  "id": "cuid",
  "firstName": "Ahmed",
  "lastName": "Benali",
  "email": "<EMAIL>",
  "avatar": "https://example.com/avatar.jpg",
  "userType": "PROVIDER",
  "isVerified": true,
  "verificationLevel": 3,
  "businessName": "Ahmed's Photography",
  "preferences": {
    "language": "fr",
    "currency": "MAD",
    "notifications": {
      "email": true,
      "push": true,
      "sms": false
    }
  }
}
```

### PUT /users/profile
Update user profile.

### GET /users/{userId}/public
Get public user profile.

### POST /users/verify-phone
Verify phone number with SMS code.

### POST /users/upload-avatar
Upload user avatar image.

---

## 🏷️ Categories & Services

### GET /categories
Get all active categories with service counts.

**Query Parameters:**
- `includeInactive`: boolean (default: false)
- `includeSubcategories`: boolean (default: true)

**Response:**
```json
[
  {
    "id": "cuid",
    "name": "Photographie & Vidéographie",
    "nameEn": "Photography & Videography",
    "icon": "📸",
    "description": "Capture de vos moments précieux",
    "order": 2,
    "coverImage": "https://example.com/category-cover.jpg",
    "_count": {
      "services": 45
    },
    "subcategories": [
      {
        "id": "cuid",
        "name": "Photographie de mariage",
        "nameEn": "Wedding Photography"
      }
    ]
  }
]
```

### GET /services
Get services with advanced filtering and search.

**Query Parameters:**
- `q`: string (search query)
- `categoryId`: string
- `subcategoryId`: string
- `city`: string
- `priceMin`: number
- `priceMax`: number
- `rating`: number (minimum rating)
- `sortBy`: "price" | "rating" | "popularity" | "newest"
- `sortOrder`: "asc" | "desc"
- `page`: number (default: 1)
- `limit`: number (default: 20)
- `featured`: boolean
- `verified`: boolean

**Response:**
```json
{
  "services": [
    {
      "id": "cuid",
      "title": "Photographe Professionnel Mariage",
      "description": "Photographe spécialisé dans les mariages...",
      "price": 3000,
      "priceType": "PER_DAY",
      "city": "Casablanca",
      "isVerified": true,
      "qualityScore": 4.8,
      "responseTime": 120,
      "provider": {
        "id": "cuid",
        "firstName": "Ahmed",
        "lastName": "Benali",
        "avatar": "https://example.com/avatar.jpg",
        "verificationLevel": 4
      },
      "category": {
        "id": "cuid",
        "name": "Photographie & Vidéographie"
      },
      "images": [
        {
          "url": "https://example.com/service-image.jpg",
          "isMain": true
        }
      ],
      "packages": [
        {
          "id": "cuid",
          "name": "Package Essentiel",
          "price": 2500,
          "duration": 8,
          "includes": ["300 photos retouchées", "Album photo"]
        }
      ],
      "_count": {
        "reviews": 23,
        "bookings": 156
      },
      "averageRating": 4.7,
      "viewCount": 1250
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 156,
    "totalPages": 8
  },
  "filters": {
    "categories": [
      { "id": "cuid", "name": "Photographie", "count": 45 }
    ],
    "cities": [
      { "name": "Casablanca", "count": 78 }
    ],
    "priceRanges": [
      { "min": 0, "max": 1000, "count": 12 },
      { "min": 1000, "max": 5000, "count": 89 }
    ]
  }
}
```

### GET /services/{serviceId}
Get detailed service information.

### POST /services
Create a new service (providers only).

### PUT /services/{serviceId}
Update service (owner only).

### DELETE /services/{serviceId}
Delete service (owner only).

### GET /services/{serviceId}/availability
Get service availability calendar.

### POST /services/{serviceId}/availability
Update service availability.

---

## 📅 Booking Management

### POST /bookings
Create a new booking.

**Request Body:**
```json
{
  "serviceId": "cuid",
  "eventDate": "2024-06-15T14:00:00Z",
  "eventType": "Wedding",
  "guestCount": 150,
  "location": "Villa Royale, Casablanca",
  "packages": ["package_id_1"],
  "addOns": [
    {
      "id": "addon_id_1",
      "quantity": 2
    }
  ],
  "specialRequests": "Need setup 2 hours early",
  "contactInfo": {
    "phone": "+************",
    "email": "<EMAIL>"
  }
}
```

**Response:**
```json
{
  "id": "cuid",
  "status": "PENDING",
  "eventDate": "2024-06-15T14:00:00Z",
  "totalAmount": 5500,
  "depositAmount": 1650,
  "remainingAmount": 3850,
  "paymentStatus": "PENDING",
  "service": {
    "id": "cuid",
    "title": "Photographe Professionnel"
  },
  "provider": {
    "id": "cuid",
    "firstName": "Ahmed",
    "lastName": "Benali"
  }
}
```

### GET /bookings
Get user's bookings (client or provider view).

### GET /bookings/{bookingId}
Get detailed booking information.

### PUT /bookings/{bookingId}/status
Update booking status (provider only).

### POST /bookings/{bookingId}/cancel
Cancel booking with cancellation policy.

---

## 💳 Payment Processing

### POST /payments/create-intent
Create payment intent for booking.

**Request Body:**
```json
{
  "bookingId": "cuid",
  "amount": 1650,
  "currency": "MAD",
  "paymentMethod": "CREDIT_CARD"
}
```

### POST /payments/confirm
Confirm payment after successful processing.

### GET /payments/{paymentId}
Get payment details.

### POST /payments/{paymentId}/refund
Process refund (admin only).

---

## ⭐ Reviews & Ratings

### POST /reviews
Create a new review.

**Request Body:**
```json
{
  "serviceId": "cuid",
  "bookingId": "cuid",
  "ratings": {
    "overall": 4.5,
    "quality": 5.0,
    "value": 4.0,
    "service": 4.5,
    "communication": 5.0
  },
  "comment": "Excellent service, très professionnel!",
  "images": ["image_url_1", "image_url_2"]
}
```

### GET /reviews
Get reviews with filtering.

### PUT /reviews/{reviewId}/helpful
Mark review as helpful.

### POST /reviews/{reviewId}/response
Provider response to review.

---

## 💬 Messaging System

### GET /conversations
Get user's conversations.

### POST /conversations
Create new conversation.

### GET /conversations/{conversationId}/messages
Get conversation messages.

### POST /conversations/{conversationId}/messages
Send new message.

### PUT /messages/{messageId}/read
Mark message as read.

---

## 🤖 AI & Recommendations

### GET /recommendations
Get personalized recommendations for user.

**Response:**
```json
[
  {
    "id": "cuid",
    "service": {
      "id": "cuid",
      "title": "Traiteur Marocain Premium"
    },
    "score": 0.89,
    "reason": "Based on your previous bookings",
    "type": "PAST_BOOKINGS"
  }
]
```

### POST /recommendations/feedback
Provide feedback on recommendations.

---

## 📊 Analytics & Insights

### GET /analytics/provider/{providerId}
Get provider business insights.

### GET /analytics/service/{serviceId}
Get service performance metrics.

### GET /analytics/market-trends
Get market trends and insights.

---

## 🔍 Search & Discovery

### GET /search
Advanced search with Elasticsearch.

### GET /search/suggestions
Get search suggestions and autocomplete.

### POST /search/track
Track search events for analytics.

---

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "message": "Email is required"
    }
  }
}
```

## Rate Limiting
- Authenticated users: 1000 requests/hour
- Anonymous users: 100 requests/hour
- Search endpoints: 500 requests/hour

## Webhooks
Available webhooks for real-time updates:
- `booking.created`
- `booking.updated`
- `payment.completed`
- `review.created`

This API documentation provides comprehensive coverage of all endpoints needed for the enhanced celebration platform.
