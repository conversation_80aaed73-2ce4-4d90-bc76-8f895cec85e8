'use client'

import { useState, useEffect } from 'react'
import { ServiceCard } from '@/components/services/service-card'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ServiceGridSkeleton } from '@/components/ui/loading-skeleton'
import { 
  Sparkles, 
  TrendingUp, 
  Heart, 
  RefreshCw,
  ChevronRight,
  Star,
  Lightbulb
} from 'lucide-react'

interface ServiceRecommendation {
  id: string
  title: string
  description: string
  price?: number
  priceType: string
  city: string
  provider: {
    firstName: string
    lastName: string
    avatar?: string
  }
  category: {
    name: string
    icon: string
  }
  images: Array<{
    url: string
    isMain: boolean
  }>
  averageRating: number
  reviewCount: number
  score: number
  reasons: string[]
}

interface RecommendationsSectionProps {
  userId?: string
  serviceId?: string
  type: 'personalized' | 'similar' | 'trending'
  title: string
  subtitle?: string
  limit?: number
  showReasons?: boolean
}

export function RecommendationsSection({
  userId,
  serviceId,
  type,
  title,
  subtitle,
  limit = 6,
  showReasons = true
}: RecommendationsSectionProps) {
  const [recommendations, setRecommendations] = useState<ServiceRecommendation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchRecommendations()
  }, [userId, serviceId, type, limit])

  const fetchRecommendations = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        type,
        limit: limit.toString()
      })

      if (userId) params.append('userId', userId)
      if (serviceId) params.append('serviceId', serviceId)

      const response = await fetch(`/api/recommendations?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch recommendations')
      }

      const data = await response.json()
      setRecommendations(data.recommendations)
    } catch (error) {
      console.error('Error fetching recommendations:', error)
      setError(error instanceof Error ? error.message : 'Failed to load recommendations')
    } finally {
      setLoading(false)
    }
  }

  const getIcon = () => {
    switch (type) {
      case 'personalized':
        return <Sparkles className="h-5 w-5" />
      case 'similar':
        return <Heart className="h-5 w-5" />
      case 'trending':
        return <TrendingUp className="h-5 w-5" />
      default:
        return <Lightbulb className="h-5 w-5" />
    }
  }

  const getIconColor = () => {
    switch (type) {
      case 'personalized':
        return 'text-purple-600'
      case 'similar':
        return 'text-red-600'
      case 'trending':
        return 'text-green-600'
      default:
        return 'text-blue-600'
    }
  }

  if (loading) {
    return (
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
            {subtitle && <div className="h-4 bg-gray-200 rounded w-96 animate-pulse"></div>}
          </div>
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <ServiceGridSkeleton count={limit} />
      </section>
    )
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-6 text-center">
          <div className="text-red-600 mb-2">⚠️ Erreur de chargement</div>
          <p className="text-red-700 mb-4">{error}</p>
          <Button onClick={fetchRecommendations} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Réessayer
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (recommendations.length === 0) {
    return (
      <Card className="border-gray-200">
        <CardContent className="p-6 text-center">
          <div className={`${getIconColor()} mb-4`}>
            {getIcon()}
          </div>
          <p className="text-gray-600">Aucune recommandation disponible pour le moment</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <section className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <span className={`mr-3 ${getIconColor()}`}>
              {getIcon()}
            </span>
            {title}
          </h2>
          {subtitle && (
            <p className="text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
        
        <Button
          onClick={fetchRecommendations}
          variant="outline"
          className="flex items-center"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Actualiser
        </Button>
      </div>

      {/* Recommendations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {recommendations.map((service) => (
          <div key={service.id} className="relative">
            <ServiceCard service={service} />
            
            {/* Recommendation Score & Reasons */}
            {showReasons && service.reasons.length > 0 && (
              <div className="absolute top-3 right-3">
                <div className="bg-white rounded-lg shadow-lg p-3 max-w-xs opacity-0 hover:opacity-100 transition-opacity duration-200">
                  <div className="flex items-center mb-2">
                    <Star className="h-4 w-4 text-yellow-500 mr-1" />
                    <span className="text-sm font-semibold">Score: {service.score.toFixed(0)}</span>
                  </div>
                  <div className="space-y-1">
                    {service.reasons.slice(0, 3).map((reason, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {reason}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Recommendation Badge */}
            <div className="absolute top-3 left-3">
              <Badge className={`${
                type === 'personalized' ? 'bg-purple-100 text-purple-800' :
                type === 'similar' ? 'bg-red-100 text-red-800' :
                'bg-green-100 text-green-800'
              }`}>
                {type === 'personalized' ? 'Pour vous' :
                 type === 'similar' ? 'Similaire' :
                 'Tendance'}
              </Badge>
            </div>
          </div>
        ))}
      </div>

      {/* Show More Button */}
      {recommendations.length >= limit && (
        <div className="text-center">
          <Button variant="outline" className="flex items-center mx-auto">
            Voir plus de recommandations
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      )}

      {/* AI Insights */}
      {type === 'personalized' && recommendations.length > 0 && (
        <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardHeader>
            <CardTitle className="flex items-center text-purple-900">
              <Lightbulb className="h-5 w-5 mr-2" />
              Insights IA
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 mb-1">
                  {recommendations.filter(r => r.reasons.includes('Correspond à vos catégories préférées')).length}
                </div>
                <div className="text-purple-700">Services dans vos catégories préférées</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 mb-1">
                  {recommendations.filter(r => r.averageRating >= 4.5).length}
                </div>
                <div className="text-blue-700">Services avec excellente note</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 mb-1">
                  {recommendations.filter(r => r.reasons.includes('Dans votre gamme de prix')).length}
                </div>
                <div className="text-green-700">Services dans votre budget</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </section>
  )
}
