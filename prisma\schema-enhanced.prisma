// Enhanced Prisma Schema for Celebration Services Marketplace Platform
// This comprehensive schema includes all advanced features for the ultimate marketplace

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql" // Changed to PostgreSQL for better scalability
  url      = env("DATABASE_URL")
}

// ============================================================================
// USER MANAGEMENT & AUTHENTICATION
// ============================================================================

model User {
  id        String   @id @default(cuid())
  firstName String
  lastName  String
  email     String   @unique
  password  String
  avatar    String?
  phone     String?
  userType  UserType @default(CLIENT)
  isActive  Boolean  @default(true)
  isVerified Boolean @default(false)
  
  // Profile enhancements
  bio          String?
  dateOfBirth  DateTime?
  gender       Gender?
  address      String?
  emergencyContact String?
  
  // Business info (for providers)
  businessName    String?
  businessLicense String?
  taxId          String?
  website        String?
  
  // Preferences
  language       String   @default("fr")
  currency       String   @default("MAD")
  timezone       String   @default("Africa/Casablanca")
  
  // Verification & trust
  verificationLevel Int    @default(0) // 0-5 trust level
  lastLoginAt      DateTime?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  services         Service[]
  bookings         Booking[]
  clientBookings   Booking[] @relation("ClientBookings")
  reviews          Review[]
  receivedReviews  Review[]  @relation("ProviderReviews")
  favorites        Favorite[]
  notifications    Notification[]
  conversations    ConversationParticipant[]
  sentMessages     Message[]
  socialProfile    SocialProfile?
  preferences      UserPreference?
  recommendations  Recommendation[]
  securityLogs     SecurityLog[]
  twoFactorAuth    TwoFactorAuth?
  businessInsights BusinessInsight[]
  competitorAnalysis CompetitorAnalysis[]
  insurance        Insurance[]
  certifications   Certification[]
  reviewVotes      ReviewVote[]
  posts            Post[]
  followers        Follow[] @relation("Followers")
  following        Follow[] @relation("Following")
  postLikes        PostLike[]
  postComments     PostComment[]
  payments         Payment[]
  collections      Collection[]

  @@map("users")
}

model UserPreference {
  id           String @id @default(cuid())
  userId       String @unique
  user         User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Preferences
  preferredCategories String // JSON array of category IDs
  budgetRange         Json   // {min: number, max: number}
  preferredCities     String // JSON array of city IDs
  eventTypes          String // JSON array: Wedding, Birthday, etc.
  stylePreferences    String // JSON array: Traditional, Modern, Luxury
  
  // Behavioral data
  viewHistory         Json   // Service IDs and timestamps
  searchHistory       Json   // Search queries and filters
  bookingHistory      Json   // Past booking patterns
  
  // Notification preferences
  emailNotifications  Boolean @default(true)
  smsNotifications    Boolean @default(false)
  pushNotifications   Boolean @default(true)
  
  updatedAt    DateTime @updatedAt
  
  @@map("user_preferences")
}

model TwoFactorAuth {
  id       String  @id @default(cuid())
  userId   String  @unique
  user     User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  secret   String
  isActive Boolean @default(false)
  backupCodes String // JSON array
  
  createdAt DateTime @default(now())
  
  @@map("two_factor_auth")
}

model SecurityLog {
  id        String   @id @default(cuid())
  userId    String?
  user      User?    @relation(fields: [userId], references: [id])
  
  action    String   // login, booking, payment, etc.
  ipAddress String
  userAgent String
  location  String?
  
  status    String   // success, failed, suspicious
  details   Json?
  
  timestamp DateTime @default(now())
  
  @@map("security_logs")
}

enum UserType {
  CLIENT
  PROVIDER
  ADMIN
  SUPER_ADMIN
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}

// ============================================================================
// LOCATION MANAGEMENT
// ============================================================================

model City {
  id     String @id @default(cuid())
  name   String
  nameEn String
  region String
  
  // Geographic data
  latitude  Float?
  longitude Float?
  timezone  String?
  
  // Metadata
  isActive  Boolean @default(true)
  population Int?
  
  @@map("cities")
}

model Translation {
  id       String @id @default(cuid())
  entityType String // service, category, etc.
  entityId   String
  field      String // name, description, etc.
  
  language   String // ISO language code
  value      String
  
  @@unique([entityType, entityId, field, language])
  @@map("translations")
}

model Currency {
  id       String @id @default(cuid())
  code     String @unique // USD, EUR, MAD
  name     String
  symbol   String
  rate     Float  // Exchange rate to base currency
  
  isActive Boolean @default(true)
  updatedAt DateTime @updatedAt
  
  @@map("currencies")
}

// ============================================================================
// CATEGORY MANAGEMENT
// ============================================================================

model Category {
  id          String  @id @default(cuid())
  name        String
  nameEn      String
  icon        String
  description String
  isActive    Boolean @default(true)
  order       Int     @default(0)
  
  // SEO & Marketing
  seoTitle       String?
  seoDescription String?
  metaKeywords   String?
  
  // Visual
  coverImage     String?
  color          String? // Hex color for theming
  
  // Relations
  services      Service[]
  subcategories Subcategory[]

  @@map("categories")
}

model Subcategory {
  id          String  @id @default(cuid())
  name        String
  nameEn      String
  description String?
  isActive    Boolean @default(true)
  order       Int     @default(0)
  
  // Relations
  categoryId String
  category   Category  @relation(fields: [categoryId], references: [id])
  services   Service[]

  @@map("subcategories")
}

// ============================================================================
// SERVICE MANAGEMENT
// ============================================================================

model Service {
  id          String        @id @default(cuid())
  title       String
  description String
  price       Float?
  priceType   PriceType     @default(FIXED)
  location    String?
  city        String
  isActive    Boolean       @default(true)
  isApproved  Boolean       @default(false)
  
  // Detailed pricing
  packages         ServicePackage[]
  addOns           ServiceAddOn[]
  
  // Availability
  availability     ServiceAvailability[]
  blackoutDates    BlackoutDate[]
  
  // Capacity & logistics
  maxCapacity      Int?
  minCapacity      Int?
  setupTime        Int? // minutes
  cleanupTime      Int? // minutes
  
  // Verification & quality
  isVerified       Boolean @default(false)
  verificationBadges String // JSON array
  qualityScore     Float?
  responseTime     Int? // average response time in minutes
  
  // SEO & marketing
  seoTitle         String?
  seoDescription   String?
  tags             String // JSON array for search
  
  // Analytics
  viewCount        Int @default(0)
  inquiryCount     Int @default(0)
  bookingCount     Int @default(0)
  
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  providerId      String
  provider        User          @relation(fields: [providerId], references: [id], onDelete: Cascade)
  categoryId      String
  category        Category      @relation(fields: [categoryId], references: [id])
  subcategoryId   String?
  subcategory     Subcategory?  @relation(fields: [subcategoryId], references: [id])
  images          ServiceImage[]
  reviews         Review[]
  bookings        Booking[]
  favorites       Favorite[]
  mediaGallery    ServiceMedia[]
  recommendations Recommendation[]
  qualityChecks   QualityCheck[]

  @@map("services")
}

model ServicePackage {
  id          String  @id @default(cuid())
  serviceId   String
  service     Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  name        String
  description String
  price       Float
  duration    Int?    // in hours
  includes    String  // JSON array
  excludes    String  // JSON array

  isPopular   Boolean @default(false)
  order       Int     @default(0)

  @@map("service_packages")
}

model ServiceAddOn {
  id          String  @id @default(cuid())
  serviceId   String
  service     Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  name        String
  description String?
  price       Float
  type        AddOnType

  isRequired  Boolean @default(false)
  maxQuantity Int?

  @@map("service_addons")
}

model ServiceAvailability {
  id        String    @id @default(cuid())
  serviceId String
  service   Service   @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  dayOfWeek Int       // 0-6 (Sunday-Saturday)
  startTime String    // HH:MM format
  endTime   String    // HH:MM format
  isActive  Boolean   @default(true)

  @@map("service_availability")
}

model BlackoutDate {
  id        String   @id @default(cuid())
  serviceId String
  service   Service  @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  date      DateTime
  reason    String?
  isRecurring Boolean @default(false)

  @@map("blackout_dates")
}

model ServiceImage {
  id        String  @id @default(cuid())
  url       String
  alt       String?
  order     Int     @default(0)
  isMain    Boolean @default(false)

  // Relations
  serviceId String
  service   Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@map("service_images")
}

model ServiceMedia {
  id        String    @id @default(cuid())
  serviceId String
  service   Service   @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  type      MediaType
  url       String
  thumbnail String?
  title     String?
  description String?
  order     Int       @default(0)

  // Metadata
  fileSize  Int?
  duration  Int?      // for videos, in seconds

  createdAt DateTime  @default(now())

  @@map("service_media")
}

enum PriceType {
  FIXED
  PER_HOUR
  PER_DAY
  NEGOTIABLE
  PACKAGE
  CUSTOM
}

enum AddOnType {
  EQUIPMENT
  STAFF
  DECORATION
  CATERING
  TRANSPORTATION
  OTHER
}

enum MediaType {
  IMAGE
  VIDEO
  AUDIO
  DOCUMENT
  VIRTUAL_TOUR
}

// ============================================================================
// ADVANCED BOOKING & PAYMENT SYSTEM
// ============================================================================

model Booking {
  id          String        @id @default(cuid())
  eventDate   DateTime
  eventType   String
  guestCount  Int?
  location    String?
  notes       String?
  status      BookingStatus @default(PENDING)

  // Enhanced payment fields
  paymentStatus    PaymentStatus @default(PENDING)
  paymentMethod    PaymentMethod?
  totalAmount      Float
  depositAmount    Float?
  remainingAmount  Float?
  cancellationFee  Float?
  refundAmount     Float?
  contractUrl      String?
  digitalSignature String?

  // Booking details
  startTime        String?   // HH:MM format
  endTime          String?   // HH:MM format
  setupRequired    Boolean   @default(false)
  specialRequests  String?

  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  clientId  String
  client    User    @relation("ClientBookings", fields: [clientId], references: [id])
  serviceId String
  service   Service @relation(fields: [serviceId], references: [id])

  // Payment tracking
  payments         Payment[]
  invoices         Invoice[]
  conversation     Conversation?

  @@map("bookings")
}

model Payment {
  id            String        @id @default(cuid())
  bookingId     String
  booking       Booking       @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  userId        String
  user          User          @relation(fields: [userId], references: [id])

  amount        Float
  method        PaymentMethod
  status        PaymentStatus
  transactionId String?
  gatewayResponse Json?

  // Payment details
  currency      String        @default("MAD")
  exchangeRate  Float?
  fees          Float?
  netAmount     Float?

  createdAt     DateTime      @default(now())
  processedAt   DateTime?

  @@map("payments")
}

model Invoice {
  id          String   @id @default(cuid())
  bookingId   String
  booking     Booking  @relation(fields: [bookingId], references: [id], onDelete: Cascade)

  invoiceNumber String @unique
  amount      Float
  taxAmount   Float?
  discountAmount Float?
  totalAmount Float

  // Invoice details
  currency    String   @default("MAD")
  dueDate     DateTime?
  pdfUrl      String?

  // Status tracking
  status      InvoiceStatus @default(DRAFT)
  sentAt      DateTime?
  paidAt      DateTime?

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("invoices")
}

enum BookingStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
  DISPUTED
}

enum PaymentMethod {
  CREDIT_CARD
  DEBIT_CARD
  BANK_TRANSFER
  CASH
  MOBILE_PAYMENT
  CRYPTOCURRENCY
  PAYPAL
  STRIPE
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

// ============================================================================
// ADVANCED REVIEW & RATING SYSTEM
// ============================================================================

model Review {
  id        String   @id @default(cuid())

  // Enhanced rating system
  overallRating       Float
  qualityRating       Float?
  valueRating         Float?
  serviceRating       Float?
  communicationRating Float?

  comment   String?

  // Rich content
  images           ReviewImage[]
  videos           ReviewVideo[]

  // Verification
  isVerified       Boolean @default(false)
  verificationMethod String?

  // Provider response
  providerResponse String?
  providerResponseAt DateTime?

  // Helpful votes
  helpfulVotes     ReviewVote[]
  helpfulCount     Int @default(0)

  // Status
  isVisible Boolean  @default(true)
  isApproved Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId    String
  user      User    @relation(fields: [userId], references: [id])
  serviceId String
  service   Service @relation(fields: [serviceId], references: [id])
  providerId String
  provider  User    @relation("ProviderReviews", fields: [providerId], references: [id])

  @@unique([userId, serviceId])
  @@map("reviews")
}

model ReviewImage {
  id       String @id @default(cuid())
  reviewId String
  review   Review @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  url      String
  caption  String?
  order    Int    @default(0)

  @@map("review_images")
}

model ReviewVideo {
  id       String @id @default(cuid())
  reviewId String
  review   Review @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  url      String
  thumbnail String?
  caption  String?
  duration Int?   // in seconds
  order    Int    @default(0)

  @@map("review_videos")
}

model ReviewVote {
  id       String @id @default(cuid())
  reviewId String
  review   Review @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  userId   String
  user     User   @relation(fields: [userId], references: [id])
  isHelpful Boolean

  createdAt DateTime @default(now())

  @@unique([reviewId, userId])
  @@map("review_votes")
}

// ============================================================================
// REAL-TIME COMMUNICATION SYSTEM
// ============================================================================

model Conversation {
  id          String    @id @default(cuid())
  bookingId   String?   @unique
  booking     Booking?  @relation(fields: [bookingId], references: [id])

  title       String?
  type        ConversationType @default(BOOKING)

  participants ConversationParticipant[]
  messages     Message[]

  status      ConversationStatus @default(ACTIVE)
  lastMessageAt DateTime?

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("conversations")
}

model ConversationParticipant {
  id             String       @id @default(cuid())
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  userId         String
  user           User         @relation(fields: [userId], references: [id])

  role           ParticipantRole
  joinedAt       DateTime     @default(now())
  lastReadAt     DateTime?
  isActive       Boolean      @default(true)

  @@unique([conversationId, userId])
  @@map("conversation_participants")
}

model Message {
  id             String       @id @default(cuid())
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  senderId       String
  sender         User         @relation(fields: [senderId], references: [id])

  content        String
  type           MessageType  @default(TEXT)
  attachments    MessageAttachment[]

  // Status tracking
  isRead         Boolean      @default(false)
  isEdited       Boolean      @default(false)
  isDeleted      Boolean      @default(false)

  // Timestamps
  readAt         DateTime?
  editedAt       DateTime?
  deletedAt      DateTime?
  createdAt      DateTime     @default(now())

  @@map("messages")
}

model MessageAttachment {
  id        String  @id @default(cuid())
  messageId String
  message   Message @relation(fields: [messageId], references: [id], onDelete: Cascade)

  url       String
  filename  String
  fileType  String
  fileSize  Int
  thumbnail String?

  @@map("message_attachments")
}

enum ConversationType {
  BOOKING
  SUPPORT
  GENERAL
  GROUP
}

enum ConversationStatus {
  ACTIVE
  ARCHIVED
  CLOSED
  BLOCKED
}

enum ParticipantRole {
  CLIENT
  PROVIDER
  ADMIN
  MODERATOR
}

enum MessageType {
  TEXT
  IMAGE
  VIDEO
  AUDIO
  DOCUMENT
  VOICE
  SYSTEM
  LOCATION
}

// ============================================================================
// SMART RECOMMENDATION ENGINE
// ============================================================================

model Recommendation {
  id        String @id @default(cuid())
  userId    String
  user      User   @relation(fields: [userId], references: [id])
  serviceId String
  service   Service @relation(fields: [serviceId], references: [id])

  score     Float   // Recommendation confidence score (0-1)
  reason    String  // Why this was recommended
  type      RecommendationType

  // Metadata
  metadata  Json?   // Additional context data
  isClicked Boolean @default(false)
  isBooked  Boolean @default(false)

  createdAt DateTime @default(now())
  expiresAt DateTime?

  @@map("recommendations")
}

enum RecommendationType {
  SIMILAR_USERS
  PAST_BOOKINGS
  TRENDING
  LOCATION_BASED
  BUDGET_MATCH
  STYLE_MATCH
  SEASONAL
  COLLABORATIVE_FILTERING
  CONTENT_BASED
}

// ============================================================================
// ADVANCED ANALYTICS & INSIGHTS
// ============================================================================

model Analytics {
  id        String      @id @default(cuid())
  entityType EntityType
  entityId   String

  metric     String      // page_view, booking_inquiry, etc.
  value      Float
  metadata   Json?       // Additional context

  // Geographic and temporal data
  country    String?
  city       String?
  device     String?
  browser    String?
  referrer   String?

  timestamp  DateTime    @default(now())

  @@index([entityType, entityId, timestamp])
  @@map("analytics")
}

model BusinessInsight {
  id         String   @id @default(cuid())
  providerId String
  provider   User     @relation(fields: [providerId], references: [id])

  period     String   // daily, weekly, monthly, yearly
  startDate  DateTime
  endDate    DateTime

  // Core metrics
  totalViews      Int
  totalInquiries  Int
  totalBookings   Int
  totalRevenue    Float
  averageRating   Float
  responseRate    Float
  conversionRate  Float

  // Advanced metrics
  customerRetentionRate Float?
  averageBookingValue   Float?
  seasonalTrends        Json?

  // Insights
  topPerformingServices String // JSON array
  peakBookingTimes     Json
  customerDemographics Json
  competitivePosition  Json

  // AI-generated insights
  aiInsights      String?
  recommendations String? // JSON array of improvement suggestions

  generatedAt DateTime @default(now())

  @@map("business_insights")
}

model MarketTrend {
  id          String   @id @default(cuid())
  category    String
  region      String

  trendType   TrendType
  metric      String   // price, demand, popularity
  value       Float
  change      Float    // percentage change

  period      String   // weekly, monthly, yearly
  date        DateTime

  insights    String?  // AI-generated insights
  confidence  Float?   // Confidence level (0-1)

  @@map("market_trends")
}

model CompetitorAnalysis {
  id           String   @id @default(cuid())
  providerId   String
  provider     User     @relation(fields: [providerId], references: [id])

  avgMarketPrice    Float
  pricePosition     String // above, below, competitive
  marketShare       Float
  strengthAreas     String // JSON array
  improvementAreas  String // JSON array

  // Competitive metrics
  rankingPosition   Int?
  reviewScore       Float?
  responseTime      Int?
  bookingRate       Float?

  generatedAt  DateTime @default(now())

  @@map("competitor_analysis")
}

enum EntityType {
  SERVICE
  PROVIDER
  CATEGORY
  CITY
  BOOKING
  REVIEW
}

enum TrendType {
  SEASONAL
  EMERGING
  DECLINING
  STABLE
  VOLATILE
}

// ============================================================================
// QUALITY ASSURANCE & TRUST
// ============================================================================

model QualityCheck {
  id         String      @id @default(cuid())
  serviceId  String
  service    Service     @relation(fields: [serviceId], references: [id])

  checkType  QualityCheckType
  status     CheckStatus
  score      Float?
  notes      String?

  // Automated checks
  automatedScore Float?
  manualScore    Float?

  checkedBy  String?
  checkedAt  DateTime?
  createdAt  DateTime    @default(now())

  @@map("quality_checks")
}

model Insurance {
  id         String   @id @default(cuid())
  providerId String
  provider   User     @relation(fields: [providerId], references: [id])

  policyNumber String
  insuranceProvider String  // Insurance company
  coverage     Float   // Coverage amount
  expiryDate   DateTime

  isActive   Boolean  @default(true)
  documents  String   // JSON array of document URLs

  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("insurance")
}

model Certification {
  id           String   @id @default(cuid())
  providerId   String
  provider     User     @relation(fields: [providerId], references: [id])

  name         String
  issuingBody  String
  issueDate    DateTime
  expiryDate   DateTime?

  certificateUrl String?
  isVerified   Boolean @default(false)
  verifiedBy   String?
  verifiedAt   DateTime?

  createdAt    DateTime @default(now())

  @@map("certifications")
}

enum QualityCheckType {
  PROFILE_VERIFICATION
  SERVICE_QUALITY
  CUSTOMER_SATISFACTION
  COMPLIANCE_CHECK
  SAFETY_INSPECTION
  INSURANCE_VERIFICATION
}

enum CheckStatus {
  PENDING
  PASSED
  FAILED
  REQUIRES_ATTENTION
  EXPIRED
}

// ============================================================================
// SOCIAL & COMMUNITY FEATURES
// ============================================================================

model SocialProfile {
  id       String @id @default(cuid())
  userId   String @unique
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  bio      String?
  website  String?
  socialLinks Json? // Instagram, Facebook, etc.

  // Social metrics
  followersCount Int @default(0)
  followingCount Int @default(0)
  postsCount     Int @default(0)

  // Verification
  isInfluencer Boolean @default(false)
  verificationBadge String?
  verifiedAt   DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("social_profiles")
}

model Post {
  id        String   @id @default(cuid())
  authorId  String
  author    User     @relation(fields: [authorId], references: [id])

  content   String
  images    String   // JSON array
  videos    String?  // JSON array
  tags      String   // JSON array

  // Engagement
  likesCount    Int @default(0)
  commentsCount Int @default(0)
  sharesCount   Int @default(0)

  // Visibility
  isPublic      Boolean @default(true)
  isPromoted    Boolean @default(false)

  likes     PostLike[]
  comments  PostComment[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("posts")
}

model PostLike {
  id     String @id @default(cuid())
  postId String
  post   Post   @relation(fields: [postId], references: [id], onDelete: Cascade)
  userId String
  user   User   @relation(fields: [userId], references: [id])

  createdAt DateTime @default(now())

  @@unique([postId, userId])
  @@map("post_likes")
}

model PostComment {
  id      String @id @default(cuid())
  postId  String
  post    Post   @relation(fields: [postId], references: [id], onDelete: Cascade)
  userId  String
  user    User   @relation(fields: [userId], references: [id])

  content String

  // Nested comments
  parentId String?
  parent   PostComment? @relation("CommentReplies", fields: [parentId], references: [id])
  replies  PostComment[] @relation("CommentReplies")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("post_comments")
}

model Follow {
  id          String @id @default(cuid())
  followerId  String
  follower    User   @relation("Following", fields: [followerId], references: [id])
  followingId String
  following   User   @relation("Followers", fields: [followingId], references: [id])

  createdAt   DateTime @default(now())

  @@unique([followerId, followingId])
  @@map("follows")
}

// ============================================================================
// FAVORITES & COLLECTIONS
// ============================================================================

model Favorite {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  serviceId String
  service   Service  @relation(fields: [serviceId], references: [id])

  // Collections
  collectionId String?
  collection   Collection? @relation(fields: [collectionId], references: [id])

  notes     String?
  createdAt DateTime @default(now())

  @@unique([userId, serviceId])
  @@map("favorites")
}

model Collection {
  id          String @id @default(cuid())
  userId      String
  user        User   @relation(fields: [userId], references: [id])

  name        String
  description String?
  isPublic    Boolean @default(false)

  favorites   Favorite[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("collections")
}

// ============================================================================
// NOTIFICATION SYSTEM
// ============================================================================

model Notification {
  id        String           @id @default(cuid())
  userId    String
  user      User             @relation(fields: [userId], references: [id])

  title     String
  message   String
  type      NotificationType

  // Rich content
  imageUrl  String?
  actionUrl String?
  metadata  Json?

  // Status
  isRead    Boolean          @default(false)
  isClicked Boolean          @default(false)

  // Delivery
  channels  String           // JSON array: email, push, sms
  sentAt    DateTime?
  readAt    DateTime?

  createdAt DateTime         @default(now())
  expiresAt DateTime?

  @@map("notifications")
}

enum NotificationType {
  BOOKING_REQUEST
  BOOKING_CONFIRMED
  BOOKING_CANCELLED
  BOOKING_COMPLETED
  PAYMENT_RECEIVED
  PAYMENT_FAILED
  REVIEW_RECEIVED
  NEW_MESSAGE
  SERVICE_APPROVED
  SERVICE_REJECTED
  PROMOTION
  SYSTEM_UPDATE
  SECURITY_ALERT
  RECOMMENDATION
  SOCIAL_ACTIVITY
}

// ============================================================================
// ADDITIONAL UTILITY MODELS
// ============================================================================

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?

  action    String   // CREATE, UPDATE, DELETE
  entity    String   // User, Service, Booking, etc.
  entityId  String

  oldValues Json?
  newValues Json?

  ipAddress String?
  userAgent String?

  timestamp DateTime @default(now())

  @@map("audit_logs")
}

model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  String // string, number, boolean, json

  description String?
  isPublic    Boolean @default(false)

  updatedAt DateTime @updatedAt

  @@map("system_config")
}
