import { NextRequest, NextResponse } from 'next/server'
import RecommendationEngine from '@/lib/ai/recommendation-engine'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const type = searchParams.get('type') || 'personalized'
    const serviceId = searchParams.get('serviceId')
    const limit = parseInt(searchParams.get('limit') || '10')

    let recommendations: any[] = []

    switch (type) {
      case 'personalized':
        if (!userId) {
          return NextResponse.json(
            { error: 'User ID is required for personalized recommendations' },
            { status: 400 }
          )
        }
        recommendations = await RecommendationEngine.getPersonalizedRecommendations(userId, limit)
        break

      case 'similar':
        if (!serviceId) {
          return NextResponse.json(
            { error: 'Service ID is required for similar recommendations' },
            { status: 400 }
          )
        }
        recommendations = await RecommendationEngine.getSimilarServices(serviceId, limit)
        break

      case 'trending':
        recommendations = await RecommendationEngine.getTrendingServices(limit)
        break

      default:
        return NextResponse.json(
          { error: 'Invalid recommendation type. Use: personalized, similar, or trending' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      type,
      count: recommendations.length,
      recommendations
    })

  } catch (error) {
    console.error('Error getting recommendations:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to get recommendations',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
