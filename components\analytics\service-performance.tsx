'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  BarChart3, 
  Eye, 
  MessageSquare, 
  Calendar, 
  DollarSign, 
  Star,
  TrendingUp,
  TrendingDown
} from 'lucide-react'

interface ServiceAnalytics {
  serviceId: string
  serviceName: string
  views: number
  inquiries: number
  bookings: number
  revenue: number
  conversionRate: number
  averageRating: number
}

interface ServicePerformanceProps {
  providerId: string
}

export function ServicePerformance({ providerId }: ServicePerformanceProps) {
  const [data, setData] = useState<ServiceAnalytics[]>([])
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30d')
  const [sortBy, setSortBy] = useState<'revenue' | 'bookings' | 'conversionRate' | 'rating'>('revenue')

  useEffect(() => {
    fetchServiceAnalytics()
  }, [providerId, period])

  const fetchServiceAnalytics = async () => {
    try {
      setLoading(true)
      
      const endDate = new Date()
      const startDate = new Date()
      
      switch (period) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1)
          break
      }

      const response = await fetch(
        `/api/analytics/provider?providerId=${providerId}&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&type=services`
      )

      if (!response.ok) {
        throw new Error('Failed to fetch service analytics')
      }

      const result = await response.json()
      setData(result.data)
    } catch (error) {
      console.error('Error fetching service analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount)
  }

  const sortedData = [...data].sort((a, b) => {
    switch (sortBy) {
      case 'revenue':
        return b.revenue - a.revenue
      case 'bookings':
        return b.bookings - a.bookings
      case 'conversionRate':
        return b.conversionRate - a.conversionRate
      case 'rating':
        return b.averageRating - a.averageRating
      default:
        return 0
    }
  })

  const getPerformanceBadge = (value: number, type: 'conversion' | 'rating') => {
    if (type === 'conversion') {
      if (value >= 20) return { label: 'Excellent', color: 'bg-green-100 text-green-800' }
      if (value >= 10) return { label: 'Bon', color: 'bg-blue-100 text-blue-800' }
      if (value >= 5) return { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800' }
      return { label: 'Faible', color: 'bg-red-100 text-red-800' }
    } else {
      if (value >= 4.5) return { label: 'Excellent', color: 'bg-green-100 text-green-800' }
      if (value >= 4.0) return { label: 'Bon', color: 'bg-blue-100 text-blue-800' }
      if (value >= 3.5) return { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800' }
      return { label: 'Faible', color: 'bg-red-100 text-red-800' }
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Performance des services
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 animate-pulse">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div className="h-5 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="grid grid-cols-4 gap-4">
                  {Array.from({ length: 4 }).map((_, j) => (
                    <div key={j} className="text-center">
                      <div className="h-4 bg-gray-200 rounded w-full mb-1"></div>
                      <div className="h-6 bg-gray-200 rounded w-3/4 mx-auto"></div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Performance des services
          </CardTitle>
          <div className="flex space-x-2">
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7 jours</SelectItem>
                <SelectItem value="30d">30 jours</SelectItem>
                <SelectItem value="90d">90 jours</SelectItem>
                <SelectItem value="1y">1 an</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="revenue">Revenus</SelectItem>
                <SelectItem value="bookings">Réservations</SelectItem>
                <SelectItem value="conversionRate">Conversion</SelectItem>
                <SelectItem value="rating">Note</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {sortedData.length > 0 ? (
          <div className="space-y-4">
            {sortedData.map((service, index) => {
              const conversionBadge = getPerformanceBadge(service.conversionRate, 'conversion')
              const ratingBadge = getPerformanceBadge(service.averageRating, 'rating')
              
              return (
                <div key={service.serviceId} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">{service.serviceName}</h4>
                      <div className="flex items-center space-x-2">
                        <Badge className={conversionBadge.color}>
                          {conversionBadge.label} conversion
                        </Badge>
                        {service.averageRating > 0 && (
                          <Badge className={ratingBadge.color}>
                            {ratingBadge.label} note
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">
                        {formatCurrency(service.revenue)}
                      </div>
                      <div className="text-sm text-gray-500">
                        #{index + 1} en {sortBy === 'revenue' ? 'revenus' : 
                          sortBy === 'bookings' ? 'réservations' : 
                          sortBy === 'conversionRate' ? 'conversion' : 'note'}
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Eye className="h-4 w-4 text-blue-500 mr-1" />
                        <span className="text-sm text-gray-600">Vues</span>
                      </div>
                      <div className="text-lg font-semibold">{service.views.toLocaleString()}</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <MessageSquare className="h-4 w-4 text-orange-500 mr-1" />
                        <span className="text-sm text-gray-600">Demandes</span>
                      </div>
                      <div className="text-lg font-semibold">{service.inquiries}</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Calendar className="h-4 w-4 text-green-500 mr-1" />
                        <span className="text-sm text-gray-600">Réservations</span>
                      </div>
                      <div className="text-lg font-semibold">{service.bookings}</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Star className="h-4 w-4 text-yellow-500 mr-1" />
                        <span className="text-sm text-gray-600">Note</span>
                      </div>
                      <div className="text-lg font-semibold">
                        {service.averageRating > 0 ? service.averageRating.toFixed(1) : 'N/A'}
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Taux de conversion:</span>
                      <div className="flex items-center">
                        {service.conversionRate >= 10 ? (
                          <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                        )}
                        <span className="font-semibold">{service.conversionRate.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <BarChart3 className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">Aucune donnée de performance pour cette période</p>
            <Button onClick={fetchServiceAnalytics} className="mt-4">
              Actualiser
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
