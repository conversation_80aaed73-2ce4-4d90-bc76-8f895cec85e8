'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  TrendingUp, 
  TrendingDown, 
  Eye, 
  MessageSquare, 
  Calendar, 
  DollarSign,
  Users,
  Star,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'

interface AnalyticsData {
  profileViews: number
  serviceViews: number
  inquiries: number
  bookings: number
  revenue: number
  inquiryToBooking: number
  averageBookingValue: number
  newCustomers: number
  repeatCustomers: number
  customerSatisfaction: number
  trends: {
    profileViews: number
    serviceViews: number
    inquiries: number
    bookings: number
    revenue: number
  }
}

interface AnalyticsOverviewProps {
  providerId: string
}

export function AnalyticsOverview({ providerId }: AnalyticsOverviewProps) {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30d')

  useEffect(() => {
    fetchAnalytics()
  }, [providerId, period])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      
      const endDate = new Date()
      const startDate = new Date()
      
      switch (period) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1)
          break
      }

      const response = await fetch(
        `/api/analytics/provider?providerId=${providerId}&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&type=overview`
      )

      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const result = await response.json()
      setData(result.data)
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  const TrendIcon = ({ trend }: { trend: number }) => {
    if (trend > 0) {
      return <ArrowUpRight className="h-4 w-4 text-green-600" />
    } else if (trend < 0) {
      return <ArrowDownRight className="h-4 w-4 text-red-600" />
    }
    return null
  }

  const TrendText = ({ trend }: { trend: number }) => {
    const color = trend > 0 ? 'text-green-600' : trend < 0 ? 'text-red-600' : 'text-gray-500'
    return (
      <span className={`text-sm ${color} flex items-center`}>
        <TrendIcon trend={trend} />
        {formatPercentage(trend)}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Tableau de bord analytique</h2>
          <div className="w-32 h-10 bg-gray-200 rounded animate-pulse"></div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-3 animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Impossible de charger les données analytiques</p>
        <Button onClick={fetchAnalytics} className="mt-4">
          Réessayer
        </Button>
      </div>
    )
  }

  const metrics = [
    {
      title: 'Vues du profil',
      value: data.profileViews.toLocaleString(),
      trend: data.trends.profileViews,
      icon: Eye,
      color: 'text-blue-600'
    },
    {
      title: 'Vues des services',
      value: data.serviceViews.toLocaleString(),
      trend: data.trends.serviceViews,
      icon: Eye,
      color: 'text-purple-600'
    },
    {
      title: 'Demandes',
      value: data.inquiries.toLocaleString(),
      trend: data.trends.inquiries,
      icon: MessageSquare,
      color: 'text-orange-600'
    },
    {
      title: 'Réservations',
      value: data.bookings.toLocaleString(),
      trend: data.trends.bookings,
      icon: Calendar,
      color: 'text-green-600'
    },
    {
      title: 'Chiffre d\'affaires',
      value: formatCurrency(data.revenue),
      trend: data.trends.revenue,
      icon: DollarSign,
      color: 'text-red-600'
    },
    {
      title: 'Taux de conversion',
      value: `${data.inquiryToBooking.toFixed(1)}%`,
      trend: 0, // Would need to calculate this
      icon: TrendingUp,
      color: 'text-indigo-600'
    },
    {
      title: 'Panier moyen',
      value: formatCurrency(data.averageBookingValue),
      trend: 0, // Would need to calculate this
      icon: DollarSign,
      color: 'text-pink-600'
    },
    {
      title: 'Satisfaction client',
      value: `${data.customerSatisfaction.toFixed(1)}/5`,
      trend: 0, // Would need to calculate this
      icon: Star,
      color: 'text-yellow-600'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Tableau de bord analytique</h2>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">7 jours</SelectItem>
            <SelectItem value="30d">30 jours</SelectItem>
            <SelectItem value="90d">90 jours</SelectItem>
            <SelectItem value="1y">1 an</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => {
          const Icon = metric.icon
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                    <p className="text-2xl font-bold text-gray-900 mt-2">{metric.value}</p>
                    {metric.trend !== 0 && (
                      <div className="mt-2">
                        <TrendText trend={metric.trend} />
                      </div>
                    )}
                  </div>
                  <div className={`p-3 rounded-full bg-gray-100 ${metric.color}`}>
                    <Icon className="h-6 w-6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Customer Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Clients
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Nouveaux clients</span>
                <span className="font-semibold">{data.newCustomers}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Clients fidèles</span>
                <span className="font-semibold">{data.repeatCustomers}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Taux de fidélité</span>
                <span className="font-semibold">
                  {((data.repeatCustomers / (data.newCustomers + data.repeatCustomers)) * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Taux de conversion</span>
                <span className="font-semibold">{data.inquiryToBooking.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Note moyenne</span>
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-500 fill-current mr-1" />
                  <span className="font-semibold">{data.customerSatisfaction.toFixed(1)}</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Panier moyen</span>
                <span className="font-semibold">{formatCurrency(data.averageBookingValue)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
