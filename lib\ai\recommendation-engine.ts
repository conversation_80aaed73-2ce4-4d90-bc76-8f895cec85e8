import { prisma } from '@/lib/prisma'

export interface UserPreferences {
  preferredCategories: string[]
  priceRange: { min: number; max: number }
  preferredCities: string[]
  eventTypes: string[]
  averageGuestCount: number
}

export interface RecommendationScore {
  serviceId: string
  score: number
  reasons: string[]
}

export interface ServiceRecommendation {
  id: string
  title: string
  description: string
  price?: number
  priceType: string
  city: string
  provider: {
    firstName: string
    lastName: string
    avatar?: string
  }
  category: {
    name: string
    icon: string
  }
  images: Array<{
    url: string
    isMain: boolean
  }>
  averageRating: number
  reviewCount: number
  score: number
  reasons: string[]
}

export class RecommendationEngine {
  /**
   * Get personalized recommendations for a user
   */
  static async getPersonalizedRecommendations(
    userId: string,
    limit: number = 10
  ): Promise<ServiceRecommendation[]> {
    try {
      // Get user preferences and behavior
      const userPreferences = await this.getUserPreferences(userId)
      const userBehavior = await this.getUserBehavior(userId)
      
      // Get all available services
      const services = await prisma.service.findMany({
        where: {
          isActive: true,
          isApproved: true
        },
        include: {
          provider: {
            select: {
              firstName: true,
              lastName: true,
              avatar: true
            }
          },
          category: {
            select: {
              name: true,
              icon: true
            }
          },
          images: {
            select: {
              url: true,
              isMain: true
            }
          },
          reviews: {
            select: {
              rating: true
            }
          },
          bookings: {
            where: {
              clientId: userId
            },
            select: {
              id: true
            }
          },
          favorites: {
            where: {
              userId: userId
            },
            select: {
              id: true
            }
          }
        }
      })

      // Filter out services user has already booked or favorited
      const availableServices = services.filter(service => 
        service.bookings.length === 0 && service.favorites.length === 0
      )

      // Calculate recommendation scores
      const scoredServices = availableServices.map(service => {
        const score = this.calculateRecommendationScore(service, userPreferences, userBehavior)
        return {
          ...service,
          score: score.score,
          reasons: score.reasons
        }
      })

      // Sort by score and take top recommendations
      const topRecommendations = scoredServices
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)

      // Format recommendations
      return topRecommendations.map(service => ({
        id: service.id,
        title: service.title,
        description: service.description,
        price: service.price,
        priceType: service.priceType,
        city: service.city,
        provider: service.provider,
        category: service.category,
        images: service.images,
        averageRating: service.reviews.length > 0
          ? service.reviews.reduce((sum, r) => sum + r.rating, 0) / service.reviews.length
          : 0,
        reviewCount: service.reviews.length,
        score: service.score,
        reasons: service.reasons
      }))

    } catch (error) {
      console.error('Error getting personalized recommendations:', error)
      throw new Error('Failed to get recommendations')
    }
  }

  /**
   * Get similar services based on a specific service
   */
  static async getSimilarServices(
    serviceId: string,
    limit: number = 6
  ): Promise<ServiceRecommendation[]> {
    try {
      // Get the reference service
      const referenceService = await prisma.service.findUnique({
        where: { id: serviceId },
        include: {
          category: true,
          subcategory: true
        }
      })

      if (!referenceService) {
        throw new Error('Reference service not found')
      }

      // Find similar services
      const similarServices = await prisma.service.findMany({
        where: {
          id: { not: serviceId },
          isActive: true,
          isApproved: true,
          OR: [
            { categoryId: referenceService.categoryId },
            { subcategoryId: referenceService.subcategoryId },
            { city: referenceService.city }
          ]
        },
        include: {
          provider: {
            select: {
              firstName: true,
              lastName: true,
              avatar: true
            }
          },
          category: {
            select: {
              name: true,
              icon: true
            }
          },
          images: {
            select: {
              url: true,
              isMain: true
            }
          },
          reviews: {
            select: {
              rating: true
            }
          }
        },
        take: limit * 2 // Get more to allow for filtering
      })

      // Calculate similarity scores
      const scoredServices = similarServices.map(service => {
        const score = this.calculateSimilarityScore(referenceService, service)
        return {
          ...service,
          score,
          reasons: this.getSimilarityReasons(referenceService, service)
        }
      })

      // Sort by similarity and take top results
      const topSimilar = scoredServices
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)

      return topSimilar.map(service => ({
        id: service.id,
        title: service.title,
        description: service.description,
        price: service.price,
        priceType: service.priceType,
        city: service.city,
        provider: service.provider,
        category: service.category,
        images: service.images,
        averageRating: service.reviews.length > 0
          ? service.reviews.reduce((sum, r) => sum + r.rating, 0) / service.reviews.length
          : 0,
        reviewCount: service.reviews.length,
        score: service.score,
        reasons: service.reasons
      }))

    } catch (error) {
      console.error('Error getting similar services:', error)
      throw new Error('Failed to get similar services')
    }
  }

  /**
   * Get trending services based on recent activity
   */
  static async getTrendingServices(limit: number = 8): Promise<ServiceRecommendation[]> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

      const services = await prisma.service.findMany({
        where: {
          isActive: true,
          isApproved: true
        },
        include: {
          provider: {
            select: {
              firstName: true,
              lastName: true,
              avatar: true
            }
          },
          category: {
            select: {
              name: true,
              icon: true
            }
          },
          images: {
            select: {
              url: true,
              isMain: true
            }
          },
          reviews: {
            select: {
              rating: true,
              createdAt: true
            }
          },
          bookings: {
            where: {
              createdAt: {
                gte: thirtyDaysAgo
              }
            },
            select: {
              id: true
            }
          },
          favorites: {
            where: {
              createdAt: {
                gte: thirtyDaysAgo
              }
            },
            select: {
              id: true
            }
          }
        }
      })

      // Calculate trending scores
      const trendingServices = services.map(service => {
        const recentBookings = service.bookings.length
        const recentFavorites = service.favorites.length
        const recentReviews = service.reviews.filter(r => r.createdAt >= thirtyDaysAgo).length
        const averageRating = service.reviews.length > 0
          ? service.reviews.reduce((sum, r) => sum + r.rating, 0) / service.reviews.length
          : 0

        // Trending score based on recent activity and quality
        const trendingScore = (recentBookings * 3) + (recentFavorites * 2) + (recentReviews * 1) + (averageRating * 2)

        return {
          ...service,
          score: trendingScore,
          reasons: this.getTrendingReasons(recentBookings, recentFavorites, recentReviews, averageRating)
        }
      })

      // Sort by trending score and take top results
      const topTrending = trendingServices
        .filter(service => service.score > 0)
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)

      return topTrending.map(service => ({
        id: service.id,
        title: service.title,
        description: service.description,
        price: service.price,
        priceType: service.priceType,
        city: service.city,
        provider: service.provider,
        category: service.category,
        images: service.images,
        averageRating: service.reviews.length > 0
          ? service.reviews.reduce((sum, r) => sum + r.rating, 0) / service.reviews.length
          : 0,
        reviewCount: service.reviews.length,
        score: service.score,
        reasons: service.reasons
      }))

    } catch (error) {
      console.error('Error getting trending services:', error)
      throw new Error('Failed to get trending services')
    }
  }

  /**
   * Get user preferences based on past behavior
   */
  private static async getUserPreferences(userId: string): Promise<UserPreferences> {
    const userBookings = await prisma.booking.findMany({
      where: { clientId: userId },
      include: {
        service: {
          include: {
            category: true
          }
        }
      }
    })

    const userFavorites = await prisma.favorite.findMany({
      where: { userId },
      include: {
        service: {
          include: {
            category: true
          }
        }
      }
    })

    // Extract preferences from user behavior
    const allServices = [...userBookings.map(b => b.service), ...userFavorites.map(f => f.service)]
    
    const preferredCategories = [...new Set(allServices.map(s => s.categoryId))]
    const preferredCities = [...new Set(allServices.map(s => s.city))]
    const eventTypes = [...new Set(userBookings.map(b => b.eventType))]
    
    const prices = allServices.filter(s => s.price).map(s => s.price!)
    const priceRange = prices.length > 0 
      ? { min: Math.min(...prices), max: Math.max(...prices) }
      : { min: 0, max: 10000 }

    const guestCounts = userBookings.filter(b => b.guestCount).map(b => b.guestCount!)
    const averageGuestCount = guestCounts.length > 0
      ? guestCounts.reduce((sum, count) => sum + count, 0) / guestCounts.length
      : 50

    return {
      preferredCategories,
      priceRange,
      preferredCities,
      eventTypes,
      averageGuestCount
    }
  }

  /**
   * Get user behavior patterns
   */
  private static async getUserBehavior(userId: string) {
    const recentBookings = await prisma.booking.findMany({
      where: {
        clientId: userId,
        createdAt: {
          gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) // Last 90 days
        }
      }
    })

    const recentFavorites = await prisma.favorite.findMany({
      where: {
        userId,
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      }
    })

    return {
      recentBookings: recentBookings.length,
      recentFavorites: recentFavorites.length,
      isActiveUser: recentBookings.length > 0 || recentFavorites.length > 0
    }
  }

  /**
   * Calculate recommendation score for a service
   */
  private static calculateRecommendationScore(
    service: any,
    preferences: UserPreferences,
    behavior: any
  ): RecommendationScore {
    let score = 0
    const reasons: string[] = []

    // Category preference match
    if (preferences.preferredCategories.includes(service.categoryId)) {
      score += 30
      reasons.push('Correspond à vos catégories préférées')
    }

    // City preference match
    if (preferences.preferredCities.includes(service.city)) {
      score += 20
      reasons.push('Dans votre ville préférée')
    }

    // Price range match
    if (service.price && service.price >= preferences.priceRange.min && service.price <= preferences.priceRange.max) {
      score += 15
      reasons.push('Dans votre gamme de prix')
    }

    // Quality score (rating)
    const averageRating = service.reviews.length > 0
      ? service.reviews.reduce((sum: number, r: any) => sum + r.rating, 0) / service.reviews.length
      : 0
    
    if (averageRating >= 4.5) {
      score += 25
      reasons.push('Excellente note client')
    } else if (averageRating >= 4.0) {
      score += 15
      reasons.push('Bonne note client')
    }

    // Popularity boost
    if (service.reviews.length >= 10) {
      score += 10
      reasons.push('Service populaire')
    }

    // New service boost
    const serviceAge = Date.now() - new Date(service.createdAt).getTime()
    const thirtyDays = 30 * 24 * 60 * 60 * 1000
    if (serviceAge < thirtyDays) {
      score += 5
      reasons.push('Nouveau service')
    }

    return { serviceId: service.id, score, reasons }
  }

  /**
   * Calculate similarity score between two services
   */
  private static calculateSimilarityScore(referenceService: any, compareService: any): number {
    let score = 0

    // Same category
    if (referenceService.categoryId === compareService.categoryId) {
      score += 40
    }

    // Same subcategory
    if (referenceService.subcategoryId && referenceService.subcategoryId === compareService.subcategoryId) {
      score += 30
    }

    // Same city
    if (referenceService.city === compareService.city) {
      score += 20
    }

    // Similar price range
    if (referenceService.price && compareService.price) {
      const priceDiff = Math.abs(referenceService.price - compareService.price)
      const avgPrice = (referenceService.price + compareService.price) / 2
      const priceVariation = priceDiff / avgPrice
      
      if (priceVariation < 0.2) { // Within 20%
        score += 10
      }
    }

    return score
  }

  /**
   * Get similarity reasons
   */
  private static getSimilarityReasons(referenceService: any, compareService: any): string[] {
    const reasons: string[] = []

    if (referenceService.categoryId === compareService.categoryId) {
      reasons.push('Même catégorie de service')
    }

    if (referenceService.city === compareService.city) {
      reasons.push('Même ville')
    }

    if (referenceService.price && compareService.price) {
      const priceDiff = Math.abs(referenceService.price - compareService.price)
      const avgPrice = (referenceService.price + compareService.price) / 2
      const priceVariation = priceDiff / avgPrice
      
      if (priceVariation < 0.2) {
        reasons.push('Prix similaire')
      }
    }

    return reasons
  }

  /**
   * Get trending reasons
   */
  private static getTrendingReasons(
    recentBookings: number,
    recentFavorites: number,
    recentReviews: number,
    averageRating: number
  ): string[] {
    const reasons: string[] = []

    if (recentBookings > 5) {
      reasons.push('Très demandé récemment')
    } else if (recentBookings > 2) {
      reasons.push('Populaire ce mois-ci')
    }

    if (recentFavorites > 3) {
      reasons.push('Souvent ajouté aux favoris')
    }

    if (recentReviews > 2) {
      reasons.push('Récemment évalué')
    }

    if (averageRating >= 4.5) {
      reasons.push('Excellente satisfaction client')
    }

    return reasons
  }
}

export default RecommendationEngine
