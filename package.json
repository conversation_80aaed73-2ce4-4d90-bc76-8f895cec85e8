{"name": "celebration-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma db push --force-reset && npm run db:seed", "docker:build": "docker build -t celebration-platform .", "docker:run": "docker run -p 3000:3000 celebration-platform", "docker:compose": "docker-compose up -d", "docker:compose:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:down": "docker-compose down", "production:setup": "npm ci --only=production && npx prisma generate && npx prisma db push", "production:start": "NODE_ENV=production npm start"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@next-auth/prisma-adapter": "^1.0.7", "@paypal/checkout-server-sdk": "^1.0.3", "@prisma/client": "^6.13.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/bcryptjs": "^2.4.6", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.536.0", "multer": "^2.0.2", "next": "15.4.5", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "sharp": "^0.34.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stripe": "^18.4.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5"}}