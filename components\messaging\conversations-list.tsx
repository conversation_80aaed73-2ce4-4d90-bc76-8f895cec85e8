'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Search, MessageCircle } from 'lucide-react'
import { useSocket } from '@/hooks/use-socket'

interface Conversation {
  id: string
  otherUser: {
    id: string
    firstName: string
    lastName: string
    avatar?: string
  }
  lastMessage?: {
    content: string
    createdAt: Date
    senderId: string
  }
  unreadCount: number
  bookingId?: string
  booking?: {
    eventType: string
    service: {
      title: string
    }
  }
}

interface ConversationsListProps {
  currentUserId: string
  onConversationSelect: (conversation: Conversation) => void
  selectedConversationId?: string
}

export function ConversationsList({
  currentUserId,
  onConversationSelect,
  selectedConversationId
}: ConversationsListProps) {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)

  const { getUnreadCount, messages } = useSocket({ userId: currentUserId })

  useEffect(() => {
    fetchConversations()
  }, [currentUserId])

  // Update unread counts when messages change
  useEffect(() => {
    updateUnreadCounts()
  }, [messages])

  const fetchConversations = async () => {
    try {
      setLoading(true)
      
      // Fetch user's bookings to get conversations
      const bookingsResponse = await fetch(`/api/bookings?userId=${currentUserId}`)
      const bookings = await bookingsResponse.json()

      // Fetch recent messages
      const messagesResponse = await fetch(`/api/messages?userId=${currentUserId}&limit=100`)
      const recentMessages = await messagesResponse.json()

      // Group conversations
      const conversationMap = new Map<string, Conversation>()

      // Add booking-based conversations
      bookings.forEach((booking: any) => {
        const otherUser = booking.clientId === currentUserId ? booking.provider : booking.client
        const conversationKey = `booking:${booking.id}`
        
        conversationMap.set(conversationKey, {
          id: conversationKey,
          otherUser,
          bookingId: booking.id,
          booking: {
            eventType: booking.eventType,
            service: booking.service
          },
          unreadCount: 0
        })
      })

      // Add direct message conversations
      recentMessages.forEach((message: any) => {
        const otherUser = message.senderId === currentUserId ? message.receiver : message.sender
        const conversationKey = `user:${otherUser.id}`
        
        if (!conversationMap.has(conversationKey)) {
          conversationMap.set(conversationKey, {
            id: conversationKey,
            otherUser,
            unreadCount: 0
          })
        }

        // Update last message
        const conversation = conversationMap.get(conversationKey)!
        if (!conversation.lastMessage || new Date(message.createdAt) > new Date(conversation.lastMessage.createdAt)) {
          conversation.lastMessage = {
            content: message.content,
            createdAt: new Date(message.createdAt),
            senderId: message.senderId
          }
        }
      })

      setConversations(Array.from(conversationMap.values()))
    } catch (error) {
      console.error('Error fetching conversations:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateUnreadCounts = () => {
    setConversations(prev => prev.map(conversation => ({
      ...conversation,
      unreadCount: getUnreadCount(conversation.otherUser.id)
    })))
  }

  const filteredConversations = conversations.filter(conversation =>
    `${conversation.otherUser.firstName} ${conversation.otherUser.lastName}`
      .toLowerCase()
      .includes(searchQuery.toLowerCase()) ||
    conversation.booking?.service.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase())
  )

  const formatTime = (date: Date) => {
    const now = new Date()
    const messageDate = new Date(date)
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
      })
    } else if (diffInHours < 168) { // 7 days
      return messageDate.toLocaleDateString('fr-FR', { weekday: 'short' })
    } else {
      return messageDate.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit'
      })
    }
  }

  const truncateMessage = (content: string, maxLength: number = 50) => {
    return content.length > maxLength ? content.substring(0, maxLength) + '...' : content
  }

  if (loading) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageCircle className="h-5 w-5 mr-2" />
            Messages
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3 animate-pulse">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <MessageCircle className="h-5 w-5 mr-2" />
            Messages
          </div>
          <Badge variant="secondary">
            {conversations.reduce((sum, conv) => sum + conv.unreadCount, 0)}
          </Badge>
        </CardTitle>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher une conversation..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="max-h-96 overflow-y-auto">
          {filteredConversations.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Aucune conversation trouvée</p>
            </div>
          ) : (
            filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => onConversationSelect(conversation)}
                className={`flex items-center space-x-3 p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 ${
                  selectedConversationId === conversation.id ? 'bg-red-50 border-red-200' : ''
                }`}
              >
                <Avatar className="h-12 w-12">
                  <AvatarImage 
                    src={conversation.otherUser.avatar} 
                    alt={`${conversation.otherUser.firstName} ${conversation.otherUser.lastName}`} 
                  />
                  <AvatarFallback>
                    {conversation.otherUser.firstName[0]}{conversation.otherUser.lastName[0]}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900 truncate">
                      {conversation.otherUser.firstName} {conversation.otherUser.lastName}
                    </h4>
                    {conversation.lastMessage && (
                      <span className="text-xs text-gray-500">
                        {formatTime(conversation.lastMessage.createdAt)}
                      </span>
                    )}
                  </div>
                  
                  {conversation.booking && (
                    <p className="text-xs text-blue-600 mb-1">
                      {conversation.booking.eventType} - {conversation.booking.service.title}
                    </p>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-600 truncate">
                      {conversation.lastMessage ? (
                        <>
                          {conversation.lastMessage.senderId === currentUserId && 'Vous: '}
                          {truncateMessage(conversation.lastMessage.content)}
                        </>
                      ) : (
                        'Aucun message'
                      )}
                    </p>
                    
                    {conversation.unreadCount > 0 && (
                      <Badge className="bg-red-600 text-white text-xs">
                        {conversation.unreadCount}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
