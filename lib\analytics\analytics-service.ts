import { prisma } from '@/lib/prisma'

export interface AnalyticsData {
  // Performance metrics
  profileViews: number
  serviceViews: number
  inquiries: number
  bookings: number
  revenue: number
  
  // Conversion metrics
  inquiryToBooking: number
  averageBookingValue: number
  
  // Customer metrics
  newCustomers: number
  repeatCustomers: number
  customerSatisfaction: number
  
  // Trends (compared to previous period)
  trends: {
    profileViews: number
    serviceViews: number
    inquiries: number
    bookings: number
    revenue: number
  }
}

export interface ServiceAnalytics {
  serviceId: string
  serviceName: string
  views: number
  inquiries: number
  bookings: number
  revenue: number
  conversionRate: number
  averageRating: number
}

export interface RevenueData {
  date: string
  revenue: number
  bookings: number
}

export interface CustomerData {
  totalCustomers: number
  newCustomers: number
  repeatCustomers: number
  topCustomers: Array<{
    id: string
    name: string
    totalSpent: number
    bookingsCount: number
  }>
}

export class AnalyticsService {
  /**
   * Get provider analytics for a specific date range
   */
  static async getProviderAnalytics(
    providerId: string,
    startDate: Date,
    endDate: Date
  ): Promise<AnalyticsData> {
    try {
      // Get current period data
      const currentData = await this.getProviderDataForPeriod(providerId, startDate, endDate)
      
      // Get previous period data for trends
      const periodLength = endDate.getTime() - startDate.getTime()
      const previousStartDate = new Date(startDate.getTime() - periodLength)
      const previousEndDate = new Date(endDate.getTime() - periodLength)
      const previousData = await this.getProviderDataForPeriod(providerId, previousStartDate, previousEndDate)
      
      // Calculate trends
      const trends = {
        profileViews: this.calculateTrend(currentData.profileViews, previousData.profileViews),
        serviceViews: this.calculateTrend(currentData.serviceViews, previousData.serviceViews),
        inquiries: this.calculateTrend(currentData.inquiries, previousData.inquiries),
        bookings: this.calculateTrend(currentData.bookings, previousData.bookings),
        revenue: this.calculateTrend(currentData.revenue, previousData.revenue)
      }
      
      return {
        ...currentData,
        trends
      }
    } catch (error) {
      console.error('Error getting provider analytics:', error)
      throw new Error('Failed to get provider analytics')
    }
  }

  /**
   * Get provider data for a specific period
   */
  private static async getProviderDataForPeriod(
    providerId: string,
    startDate: Date,
    endDate: Date
  ) {
    // Get bookings data
    const bookings = await prisma.booking.findMany({
      where: {
        providerId,
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        client: true,
        service: true
      }
    })

    // Get services data
    const services = await prisma.service.findMany({
      where: {
        providerId,
        createdAt: {
          lte: endDate
        }
      },
      include: {
        reviews: {
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          }
        }
      }
    })

    // Calculate metrics
    const revenue = bookings
      .filter(b => b.status === 'COMPLETED')
      .reduce((sum, b) => sum + (b.totalPrice || 0), 0)

    const completedBookings = bookings.filter(b => b.status === 'COMPLETED').length
    const totalBookings = bookings.length
    const inquiries = bookings.length // Assuming each booking starts as an inquiry

    // Get unique customers
    const customerIds = new Set(bookings.map(b => b.clientId))
    const totalCustomers = customerIds.size

    // Get repeat customers (customers with more than one booking)
    const customerBookingCounts = new Map<string, number>()
    bookings.forEach(booking => {
      const count = customerBookingCounts.get(booking.clientId) || 0
      customerBookingCounts.set(booking.clientId, count + 1)
    })
    const repeatCustomers = Array.from(customerBookingCounts.values())
      .filter(count => count > 1).length

    // Calculate average rating
    const allReviews = services.flatMap(s => s.reviews)
    const averageRating = allReviews.length > 0
      ? allReviews.reduce((sum, r) => sum + r.rating, 0) / allReviews.length
      : 0

    return {
      profileViews: 0, // Would come from analytics tracking
      serviceViews: 0, // Would come from analytics tracking
      inquiries,
      bookings: totalBookings,
      revenue,
      inquiryToBooking: inquiries > 0 ? (totalBookings / inquiries) * 100 : 0,
      averageBookingValue: totalBookings > 0 ? revenue / totalBookings : 0,
      newCustomers: totalCustomers - repeatCustomers,
      repeatCustomers,
      customerSatisfaction: averageRating
    }
  }

  /**
   * Get service-specific analytics
   */
  static async getServiceAnalytics(
    providerId: string,
    startDate: Date,
    endDate: Date
  ): Promise<ServiceAnalytics[]> {
    try {
      const services = await prisma.service.findMany({
        where: { providerId },
        include: {
          bookings: {
            where: {
              createdAt: {
                gte: startDate,
                lte: endDate
              }
            }
          },
          reviews: {
            where: {
              createdAt: {
                gte: startDate,
                lte: endDate
              }
            }
          }
        }
      })

      return services.map(service => {
        const bookings = service.bookings
        const completedBookings = bookings.filter(b => b.status === 'COMPLETED')
        const revenue = completedBookings.reduce((sum, b) => sum + (b.totalPrice || 0), 0)
        const averageRating = service.reviews.length > 0
          ? service.reviews.reduce((sum, r) => sum + r.rating, 0) / service.reviews.length
          : 0

        return {
          serviceId: service.id,
          serviceName: service.title,
          views: service.viewCount || 0,
          inquiries: bookings.length,
          bookings: completedBookings.length,
          revenue,
          conversionRate: bookings.length > 0 ? (completedBookings.length / bookings.length) * 100 : 0,
          averageRating
        }
      })
    } catch (error) {
      console.error('Error getting service analytics:', error)
      throw new Error('Failed to get service analytics')
    }
  }

  /**
   * Get revenue data for charts
   */
  static async getRevenueData(
    providerId: string,
    startDate: Date,
    endDate: Date,
    groupBy: 'day' | 'week' | 'month' = 'day'
  ): Promise<RevenueData[]> {
    try {
      const bookings = await prisma.booking.findMany({
        where: {
          providerId,
          status: 'COMPLETED',
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      })

      // Group bookings by date
      const groupedData = new Map<string, { revenue: number; bookings: number }>()

      bookings.forEach(booking => {
        const date = this.formatDateForGrouping(booking.createdAt, groupBy)
        const existing = groupedData.get(date) || { revenue: 0, bookings: 0 }
        
        groupedData.set(date, {
          revenue: existing.revenue + (booking.totalPrice || 0),
          bookings: existing.bookings + 1
        })
      })

      // Convert to array and sort by date
      return Array.from(groupedData.entries())
        .map(([date, data]) => ({
          date,
          revenue: data.revenue,
          bookings: data.bookings
        }))
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    } catch (error) {
      console.error('Error getting revenue data:', error)
      throw new Error('Failed to get revenue data')
    }
  }

  /**
   * Get customer analytics
   */
  static async getCustomerAnalytics(
    providerId: string,
    startDate: Date,
    endDate: Date
  ): Promise<CustomerData> {
    try {
      const bookings = await prisma.booking.findMany({
        where: {
          providerId,
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        include: {
          client: true
        }
      })

      // Calculate customer metrics
      const customerMap = new Map<string, {
        id: string
        name: string
        totalSpent: number
        bookingsCount: number
      }>()

      bookings.forEach(booking => {
        const clientId = booking.clientId
        const existing = customerMap.get(clientId) || {
          id: clientId,
          name: `${booking.client.firstName} ${booking.client.lastName}`,
          totalSpent: 0,
          bookingsCount: 0
        }

        customerMap.set(clientId, {
          ...existing,
          totalSpent: existing.totalSpent + (booking.totalPrice || 0),
          bookingsCount: existing.bookingsCount + 1
        })
      })

      const customers = Array.from(customerMap.values())
      const newCustomers = customers.filter(c => c.bookingsCount === 1).length
      const repeatCustomers = customers.filter(c => c.bookingsCount > 1).length

      // Get top customers by total spent
      const topCustomers = customers
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, 10)

      return {
        totalCustomers: customers.length,
        newCustomers,
        repeatCustomers,
        topCustomers
      }
    } catch (error) {
      console.error('Error getting customer analytics:', error)
      throw new Error('Failed to get customer analytics')
    }
  }

  /**
   * Calculate trend percentage
   */
  private static calculateTrend(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / previous) * 100
  }

  /**
   * Format date for grouping
   */
  private static formatDateForGrouping(date: Date, groupBy: 'day' | 'week' | 'month'): string {
    switch (groupBy) {
      case 'day':
        return date.toISOString().split('T')[0]
      case 'week':
        const weekStart = new Date(date)
        weekStart.setDate(date.getDate() - date.getDay())
        return weekStart.toISOString().split('T')[0]
      case 'month':
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      default:
        return date.toISOString().split('T')[0]
    }
  }
}

export default AnalyticsService
