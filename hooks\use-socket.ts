'use client'

import { useEffect, useRef, useState } from 'react'
import { io, Socket } from 'socket.io-client'

export interface Message {
  id: string
  content: string
  senderId: string
  receiverId: string
  bookingId?: string
  type: 'text' | 'image' | 'file' | 'system'
  metadata?: Record<string, any>
  isRead: boolean
  readAt?: Date
  createdAt: Date
  sender: {
    id: string
    firstName: string
    lastName: string
    avatar?: string
  }
}

export interface TypingUser {
  userId: string
  userName: string
  bookingId?: string
}

interface UseSocketProps {
  userId?: string
  token?: string
}

export function useSocket({ userId, token }: UseSocketProps = {}) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([])
  const [onlineUsers, setOnlineUsers] = useState<string[]>([])

  const socketRef = useRef<Socket | null>(null)

  useEffect(() => {
    if (!userId) return

    // Initialize socket connection
    const socketInstance = io(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000', {
      path: '/api/socket',
      addTrailingSlash: false,
    })

    socketRef.current = socketInstance
    setSocket(socketInstance)

    // Connection event handlers
    socketInstance.on('connect', () => {
      console.log('Connected to socket server')
      setIsConnected(true)
      
      // Authenticate user
      socketInstance.emit('authenticate', { userId, token })
    })

    socketInstance.on('disconnect', () => {
      console.log('Disconnected from socket server')
      setIsConnected(false)
      setIsAuthenticated(false)
    })

    socketInstance.on('authenticated', (data) => {
      console.log('User authenticated:', data.user)
      setIsAuthenticated(true)
    })

    socketInstance.on('authentication_error', (error) => {
      console.error('Authentication error:', error)
      setIsAuthenticated(false)
    })

    // Message event handlers
    socketInstance.on('new_message', (message: Message) => {
      setMessages(prev => [...prev, message])
    })

    socketInstance.on('message_sent', (message: Message) => {
      setMessages(prev => [...prev, message])
    })

    socketInstance.on('conversation_history', (history: Message[]) => {
      setMessages(history)
    })

    socketInstance.on('system_message', (message: Message) => {
      setMessages(prev => [...prev, message])
    })

    socketInstance.on('booking_message', (message: Message) => {
      setMessages(prev => [...prev, message])
    })

    // Typing event handlers
    socketInstance.on('user_typing', (data: TypingUser) => {
      setTypingUsers(prev => {
        const filtered = prev.filter(user => user.userId !== data.userId)
        return [...filtered, data]
      })
    })

    socketInstance.on('user_stopped_typing', (data: { userId: string; bookingId?: string }) => {
      setTypingUsers(prev => prev.filter(user => user.userId !== data.userId))
    })

    // Read status handlers
    socketInstance.on('messages_marked_read', (data: { messageIds: string[] }) => {
      setMessages(prev => prev.map(msg => 
        data.messageIds.includes(msg.id) 
          ? { ...msg, isRead: true, readAt: new Date() }
          : msg
      ))
    })

    // Error handler
    socketInstance.on('error', (error) => {
      console.error('Socket error:', error)
    })

    return () => {
      socketInstance.disconnect()
    }
  }, [userId, token])

  // Send message
  const sendMessage = (data: {
    content: string
    receiverId: string
    bookingId?: string
    type?: 'text' | 'image' | 'file'
    metadata?: Record<string, any>
  }) => {
    if (!socket || !isAuthenticated) {
      console.error('Socket not connected or not authenticated')
      return
    }

    socket.emit('send_message', {
      content: data.content,
      receiverId: data.receiverId,
      bookingId: data.bookingId,
      type: data.type || 'text',
      metadata: data.metadata
    })
  }

  // Join conversation
  const joinConversation = (data: { userId: string; bookingId?: string }) => {
    if (!socket || !isAuthenticated) {
      console.error('Socket not connected or not authenticated')
      return
    }

    setMessages([]) // Clear previous messages
    socket.emit('join_conversation', data)
  }

  // Start typing
  const startTyping = (data: { receiverId: string; bookingId?: string }) => {
    if (!socket || !isAuthenticated) return
    socket.emit('typing_start', data)
  }

  // Stop typing
  const stopTyping = (data: { receiverId: string; bookingId?: string }) => {
    if (!socket || !isAuthenticated) return
    socket.emit('typing_stop', data)
  }

  // Mark messages as read
  const markAsRead = (messageIds: string[]) => {
    if (!socket || !isAuthenticated) return
    socket.emit('mark_as_read', { messageIds })
  }

  // Get unread message count
  const getUnreadCount = (fromUserId?: string): number => {
    return messages.filter(msg => 
      !msg.isRead && 
      msg.receiverId === userId &&
      (!fromUserId || msg.senderId === fromUserId)
    ).length
  }

  // Get conversation messages
  const getConversationMessages = (otherUserId: string, bookingId?: string): Message[] => {
    return messages.filter(msg => {
      if (bookingId) {
        return msg.bookingId === bookingId
      }
      return (
        (msg.senderId === userId && msg.receiverId === otherUserId) ||
        (msg.senderId === otherUserId && msg.receiverId === userId)
      )
    })
  }

  // Check if user is typing
  const isUserTyping = (otherUserId: string, bookingId?: string): boolean => {
    return typingUsers.some(user => 
      user.userId === otherUserId && 
      (!bookingId || user.bookingId === bookingId)
    )
  }

  return {
    socket,
    isConnected,
    isAuthenticated,
    messages,
    typingUsers,
    onlineUsers,
    sendMessage,
    joinConversation,
    startTyping,
    stopTyping,
    markAsRead,
    getUnreadCount,
    getConversationMessages,
    isUserTyping
  }
}

export default useSocket
