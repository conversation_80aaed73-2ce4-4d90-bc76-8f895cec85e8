import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create Moroccan cities
  const cities = [
    { name: 'Casablanca', nameEn: 'Casablanca', region: 'Casablanca-Settat' },
    { name: 'Rabat', nameEn: 'Rabat', region: 'Rabat-Salé-Kénitra' },
    { name: 'Marrakech', nameEn: 'Marrakech', region: 'Marrakech-Safi' },
    { name: '<PERSON><PERSON>', nameEn: 'Fez', region: 'Fès-Meknès' },
    { name: 'Tanger', nameEn: 'Tangier', region: 'Tanger-Tétouan-Al Hoceïma' },
    { name: 'Agadir', nameEn: 'Agadir', region: 'Souss-Massa' },
    { name: 'Meknès', nameEn: 'Meknes', region: 'Fès-Meknès' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', nameEn: 'Oujda', region: 'Oriental' },
    { name: 'Kénitra', nameEn: 'Kenitra', region: 'Rabat-Salé-Kénitra' },
    { name: 'Tétouan', nameEn: 'Tetouan', region: 'Tanger-Tétouan-Al Hoceïma' },
    { name: 'Salé', nameEn: 'Sale', region: 'Rabat-Salé-Kénitra' },
    { name: 'Temara', nameEn: 'Temara', region: 'Rabat-Salé-Kénitra' },
    { name: 'Mohammedia', nameEn: 'Mohammedia', region: 'Casablanca-Settat' },
    { name: 'El Jadida', nameEn: 'El Jadida', region: 'Casablanca-Settat' },
    { name: 'Beni Mellal', nameEn: 'Beni Mellal', region: 'Béni Mellal-Khénifra' }
  ]

  for (const city of cities) {
    await prisma.city.upsert({
      where: { name: city.name },
      update: {},
      create: city
    })
  }

  // Create categories with subcategories
  const categoriesData = [
    {
      name: 'Lieux',
      nameEn: 'Venues',
      icon: '🏛️',
      description: 'Salles et espaces pour vos événements',
      subcategories: [
        { name: 'Mariage', nameEn: 'Wedding' },
        { name: 'Fiançailles', nameEn: 'Engagement' },
        { name: 'Anniversaire', nameEn: 'Birthday' },
        { name: 'Baby Shower', nameEn: 'Baby Shower' },
        { name: 'Espaces Extérieurs', nameEn: 'Outdoor Spaces' }
      ]
    },
    {
      name: 'Décoration',
      nameEn: 'Decoration',
      icon: '✨',
      description: 'Décoration et aménagement d\'espaces',
      subcategories: [
        { name: 'Mariage', nameEn: 'Wedding' },
        { name: 'Anniversaire', nameEn: 'Birthday' },
        { name: 'Soirée Henné', nameEn: 'Henna Party' },
        { name: 'Ballons & Fleurs', nameEn: 'Balloon & Floral' }
      ]
    },
    {
      name: 'Photographie & Vidéographie',
      nameEn: 'Photography & Videography',
      icon: '📸',
      description: 'Capture de vos moments précieux',
      subcategories: [
        { name: 'Mariage', nameEn: 'Wedding' },
        { name: 'Cabines 360', nameEn: '360 Booths' },
        { name: 'Impression Instantanée', nameEn: 'Instant Print' }
      ]
    },
    {
      name: 'Traiteur',
      nameEn: 'Catering',
      icon: '🍽️',
      description: 'Services de restauration et pâtisserie',
      subcategories: [
        { name: 'Cuisine Marocaine', nameEn: 'Moroccan Cuisine' },
        { name: 'Gâteaux & Pâtisseries', nameEn: 'Cakes & Pastries' },
        { name: 'Gâteaux de Célébration', nameEn: 'Celebration Cakes' }
      ]
    },
    {
      name: 'Musique & Animation',
      nameEn: 'Music & Animation',
      icon: '🎵',
      description: 'Animation musicale et spectacles',
      subcategories: [
        { name: 'DJs', nameEn: 'DJs' },
        { name: 'Groupes Traditionnels (Dakaka, Issawa)', nameEn: 'Traditional Bands (Dakaka, Issawa)' },
        { name: 'Animation Enfants', nameEn: 'Kids Entertainment' }
      ]
    },
    {
      name: 'Beauté & Coiffure',
      nameEn: 'Beauty & Styling',
      icon: '💄',
      description: 'Services de beauté et bien-être',
      subcategories: [
        { name: 'Maquillage (Hommes/Femmes)', nameEn: 'Makeup (Men/Women)' },
        { name: 'Artistes Henné', nameEn: 'Henna Artists' },
        { name: 'Coiffeurs', nameEn: 'Hair Stylists' },
        { name: 'Hammams', nameEn: 'Hammams' }
      ]
    },
    {
      name: 'Vêtements Traditionnels',
      nameEn: 'Traditional Clothing',
      icon: '👘',
      description: 'Tenues traditionnelles marocaines',
      subcategories: [
        { name: 'Caftan/Takchita', nameEn: 'Caftan/Takchita' },
        { name: 'Jellaba/Jabador', nameEn: 'Jellaba/Jabador' },
        { name: 'Accessoires', nameEn: 'Accessories' }
      ]
    },
    {
      name: 'Transport',
      nameEn: 'Transport',
      icon: '🚗',
      description: 'Services de transport pour événements',
      subcategories: [
        { name: 'Voitures de Luxe', nameEn: 'Luxury Cars' },
        { name: 'Transport d\'Invités', nameEn: 'Guest Transport' }
      ]
    },
    {
      name: 'Cadeaux & Invitations',
      nameEn: 'Gifts & Invitations',
      icon: '🎁',
      description: 'Cadeaux et faire-part personnalisés',
      subcategories: [
        { name: 'Boîtes Cadeaux', nameEn: 'Gift Boxes' },
        { name: 'Cartes d\'Invitation', nameEn: 'Invitation Cards' }
      ]
    }
  ]

  for (const categoryData of categoriesData) {
    const { subcategories, ...categoryInfo } = categoryData
    
    const category = await prisma.category.upsert({
      where: { name: categoryInfo.name },
      update: {},
      create: {
        ...categoryInfo,
        order: categoriesData.indexOf(categoryData)
      }
    })

    for (const subcategoryData of subcategories) {
      const existingSubcategory = await prisma.subcategory.findFirst({
        where: {
          categoryId: category.id,
          name: subcategoryData.name
        }
      })

      if (!existingSubcategory) {
        await prisma.subcategory.create({
          data: {
            ...subcategoryData,
            categoryId: category.id,
            order: subcategories.indexOf(subcategoryData)
          }
        })
      }
    }
  }

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'Platform',
      role: 'ADMIN',
      city: 'Casablanca',
      isActive: true
    }
  })

  // Create sample provider user
  const providerPassword = await bcrypt.hash('provider123', 12)
  
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: providerPassword,
      firstName: 'Ahmed',
      lastName: 'Benali',
      role: 'PROVIDER',
      city: 'Casablanca',
      phone: '+212 6 12 34 56 78',
      isActive: true
    }
  })

  // Create sample client user
  const clientPassword = await bcrypt.hash('client123', 12)
  
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: clientPassword,
      firstName: 'Fatima',
      lastName: 'Alami',
      role: 'CLIENT',
      city: 'Rabat',
      phone: '+212 6 87 65 43 21',
      isActive: true
    }
  })

  // Create sample services
  const venuesCategory = await prisma.category.findFirst({ where: { name: 'Lieux' } })
  const decorationCategory = await prisma.category.findFirst({ where: { name: 'Décoration' } })
  const photographyCategory = await prisma.category.findFirst({ where: { name: 'Photographie & Vidéographie' } })
  const cateringCategory = await prisma.category.findFirst({ where: { name: 'Traiteur' } })

  const providerUser = await prisma.user.findFirst({ where: { email: '<EMAIL>' } })

  if (venuesCategory && providerUser) {
    // Create sample services
    const sampleServices = [
      {
        title: 'Villa Royale - Salle de Mariage',
        description: 'Magnifique villa avec jardin pour vos mariages et célébrations. Capacité jusqu\'à 200 personnes.',
        price: 15000,
        priceType: 'FIXED',
        city: 'Casablanca',
        categoryId: venuesCategory.id,
        providerId: providerUser.id,
        isActive: true,
        images: {
          create: [
            { url: '/images/villa-royale-1.jpg', isMain: true },
            { url: '/images/villa-royale-2.jpg', isMain: false }
          ]
        }
      },
      {
        title: 'Décoration Florale Premium',
        description: 'Service de décoration florale haut de gamme pour mariages et événements spéciaux.',
        price: 5000,
        priceType: 'NEGOTIABLE',
        city: 'Rabat',
        categoryId: decorationCategory?.id,
        providerId: providerUser.id,
        isActive: true,
        images: {
          create: [
            { url: '/images/decoration-1.jpg', isMain: true }
          ]
        }
      },
      {
        title: 'Photographe Professionnel',
        description: 'Photographe spécialisé dans les mariages et événements traditionnels marocains.',
        price: 3000,
        priceType: 'PER_DAY',
        city: 'Marrakech',
        categoryId: photographyCategory?.id,
        providerId: providerUser.id,
        isActive: true,
        images: {
          create: [
            { url: '/images/photographer-1.jpg', isMain: true }
          ]
        }
      },
      {
        title: 'Traiteur Marocain Traditionnel',
        description: 'Service de traiteur spécialisé dans la cuisine marocaine authentique.',
        price: 200,
        priceType: 'PER_HOUR',
        city: 'Casablanca',
        categoryId: cateringCategory?.id,
        providerId: providerUser.id,
        isActive: true,
        images: {
          create: [
            { url: '/images/catering-1.jpg', isMain: true }
          ]
        }
      }
    ]

    for (const serviceData of sampleServices) {
      if (serviceData.categoryId) {
        await prisma.service.create({
          data: serviceData
        })
      }
    }
  }

  console.log('✅ Database seeding completed!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

