# 🔧 Technical Specifications - Enhanced Celebration Platform

## Architecture Overview

### Technology Stack
- **Frontend**: Next.js 15, React 18, TypeScript
- **Backend**: Next.js API Routes, Node.js
- **Database**: PostgreSQL (migrated from SQLite)
- **ORM**: Prisma
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS
- **State Management**: Zustand/Redux Toolkit
- **Real-time**: Socket.io
- **Search**: Elasticsearch
- **File Storage**: AWS S3/Cloudinary
- **Payments**: Stripe, PayPal
- **Deployment**: Vercel/AWS

### Database Schema Enhancements

#### Core Models
```prisma
// Enhanced User model with comprehensive profile
model User {
  // Basic info + business details + verification
  // Social features + preferences + security
}

// Advanced Service model with packages and availability
model Service {
  // Detailed pricing + availability + quality metrics
  // SEO optimization + analytics + media gallery
}

// Comprehensive Booking system with payments
model Booking {
  // Multi-step booking + payment tracking
  // Contract management + status tracking
}
```

#### New Advanced Models
- **ServicePackage**: Tiered pricing options
- **ServiceAddOn**: Additional services and equipment
- **ServiceAvailability**: Time-based availability management
- **Payment**: Comprehensive payment tracking
- **Invoice**: Automated billing system
- **Conversation**: Real-time messaging
- **Recommendation**: AI-powered suggestions
- **Analytics**: Detailed usage tracking
- **QualityCheck**: Automated quality assurance

### API Architecture

#### RESTful Endpoints
```typescript
// Enhanced API structure
/api/v1/
├── auth/           # Authentication & authorization
├── users/          # User management
├── services/       # Service CRUD operations
├── bookings/       # Booking management
├── payments/       # Payment processing
├── reviews/        # Review system
├── messages/       # Real-time messaging
├── analytics/      # Analytics & insights
├── recommendations/ # AI recommendations
└── admin/          # Administrative functions
```

#### GraphQL Integration
```graphql
type Query {
  services(filters: ServiceFilters): [Service!]!
  recommendations(userId: ID!): [Recommendation!]!
  analytics(period: Period!): Analytics!
}

type Mutation {
  createBooking(input: BookingInput!): Booking!
  processPayment(input: PaymentInput!): Payment!
  sendMessage(input: MessageInput!): Message!
}

type Subscription {
  messageAdded(conversationId: ID!): Message!
  bookingUpdated(bookingId: ID!): Booking!
}
```

### Frontend Architecture

#### Component Structure
```
components/
├── ui/              # Reusable UI components
├── forms/           # Form components
├── layout/          # Layout components
├── features/        # Feature-specific components
│   ├── booking/
│   ├── messaging/
│   ├── reviews/
│   └── analytics/
└── providers/       # Context providers
```

#### State Management
```typescript
// Zustand stores
interface AppState {
  user: UserState
  services: ServicesState
  bookings: BookingsState
  messages: MessagesState
  ui: UIState
}

// Redux Toolkit slices (alternative)
const store = configureStore({
  reducer: {
    auth: authSlice.reducer,
    services: servicesSlice.reducer,
    bookings: bookingsSlice.reducer,
  }
})
```

### Real-time Features

#### WebSocket Implementation
```typescript
// Socket.io integration
const socket = io('/api/socket')

// Real-time events
socket.on('message:new', handleNewMessage)
socket.on('booking:updated', handleBookingUpdate)
socket.on('notification:new', handleNotification)
```

#### Push Notifications
```typescript
// Service Worker for push notifications
self.addEventListener('push', (event) => {
  const data = event.data.json()
  self.registration.showNotification(data.title, {
    body: data.body,
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png'
  })
})
```

### Search & Discovery

#### Elasticsearch Integration
```typescript
// Search configuration
const searchConfig = {
  index: 'services',
  body: {
    query: {
      multi_match: {
        query: searchTerm,
        fields: ['title^2', 'description', 'tags']
      }
    },
    aggs: {
      categories: { terms: { field: 'category' } },
      price_ranges: { range: { field: 'price' } }
    }
  }
}
```

#### AI-Powered Recommendations
```typescript
// Recommendation engine
class RecommendationEngine {
  async generateRecommendations(userId: string) {
    const userProfile = await this.getUserProfile(userId)
    const collaborativeFiltering = await this.collaborativeFiltering(userId)
    const contentBased = await this.contentBasedFiltering(userProfile)
    
    return this.combineRecommendations([
      collaborativeFiltering,
      contentBased
    ])
  }
}
```

### Payment System

#### Multi-Gateway Integration
```typescript
// Payment processor abstraction
interface PaymentGateway {
  processPayment(amount: number, method: PaymentMethod): Promise<PaymentResult>
  refundPayment(transactionId: string): Promise<RefundResult>
  getTransactionStatus(transactionId: string): Promise<TransactionStatus>
}

class StripeGateway implements PaymentGateway {
  // Stripe implementation
}

class PayPalGateway implements PaymentGateway {
  // PayPal implementation
}
```

#### Moroccan Payment Methods
```typescript
// Local payment integration
const moroccanPaymentMethods = {
  CIH_BANK: 'cih_bank_transfer',
  ATTIJARIWAFA: 'attijariwafa_bank',
  BMCE: 'bmce_bank',
  CASH_PLUS: 'cash_plus',
  ORANGE_MONEY: 'orange_money'
}
```

### Security Implementation

#### Authentication & Authorization
```typescript
// JWT token management
const authConfig = {
  providers: [
    CredentialsProvider({
      credentials: { email: {}, password: {} },
      authorize: async (credentials) => {
        // Custom authentication logic
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET
    })
  ],
  callbacks: {
    jwt: ({ token, user }) => ({ ...token, ...user }),
    session: ({ session, token }) => ({ ...session, user: token })
  }
}
```

#### Data Protection
```typescript
// Encryption utilities
class SecurityUtils {
  static encryptSensitiveData(data: string): string {
    return crypto.encrypt(data, process.env.ENCRYPTION_KEY)
  }
  
  static hashPassword(password: string): string {
    return bcrypt.hash(password, 12)
  }
  
  static sanitizeInput(input: string): string {
    return DOMPurify.sanitize(input)
  }
}
```

### Performance Optimization

#### Caching Strategy
```typescript
// Redis caching
const cacheConfig = {
  services: { ttl: 300 }, // 5 minutes
  categories: { ttl: 3600 }, // 1 hour
  user_preferences: { ttl: 1800 }, // 30 minutes
  search_results: { ttl: 600 } // 10 minutes
}

// Next.js ISR
export async function getStaticProps() {
  return {
    props: { services },
    revalidate: 300 // 5 minutes
  }
}
```

#### Image Optimization
```typescript
// Cloudinary integration
const cloudinaryConfig = {
  transformations: {
    thumbnail: 'w_300,h_200,c_fill,q_auto',
    medium: 'w_800,h_600,c_fill,q_auto',
    large: 'w_1200,h_900,c_fill,q_auto'
  }
}
```

### Analytics & Monitoring

#### Event Tracking
```typescript
// Analytics service
class AnalyticsService {
  trackEvent(event: string, properties: Record<string, any>) {
    // Google Analytics 4
    gtag('event', event, properties)
    
    // Custom analytics
    this.sendToCustomAnalytics(event, properties)
  }
  
  trackPageView(page: string) {
    this.trackEvent('page_view', { page })
  }
  
  trackBooking(bookingData: BookingData) {
    this.trackEvent('booking_created', bookingData)
  }
}
```

#### Performance Monitoring
```typescript
// Application monitoring
const monitoringConfig = {
  sentry: {
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV
  },
  newRelic: {
    licenseKey: process.env.NEW_RELIC_LICENSE_KEY
  }
}
```

### Testing Strategy

#### Test Structure
```
tests/
├── unit/           # Unit tests
├── integration/    # Integration tests
├── e2e/           # End-to-end tests
└── performance/   # Performance tests
```

#### Testing Tools
- **Unit**: Jest, React Testing Library
- **Integration**: Supertest, Prisma Test Environment
- **E2E**: Playwright, Cypress
- **Performance**: Lighthouse CI, WebPageTest

### Deployment & DevOps

#### CI/CD Pipeline
```yaml
# GitHub Actions workflow
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: npm test
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
```

#### Infrastructure as Code
```typescript
// Terraform configuration
resource "aws_s3_bucket" "media_storage" {
  bucket = "celebration-platform-media"
}

resource "aws_cloudfront_distribution" "cdn" {
  origin {
    domain_name = aws_s3_bucket.media_storage.bucket_regional_domain_name
    origin_id   = "S3-celebration-platform-media"
  }
}
```

This technical specification provides the foundation for implementing all the advanced features outlined in the enhanced schema and roadmap.
